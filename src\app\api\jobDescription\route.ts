import { JOBS_STATUS } from "@/Enums"; 
import { DynamoDBtable } from "@/lib/db_actions";
import axios from "axios";
import { NextRequest, NextResponse } from "next/server";
import { z } from "zod"; 
const jobDescriptionTable = new DynamoDBtable("LCPJobDes");

const jobDescSchema = z.object({
  created_by: z.string(),
  skills: z.string(),
  status: z.enum(Object.values(JOBS_STATUS) as [string]),
  jobtitle: z.string().min(1),
  responsibilities: z.string().min(1),
  location: z.string().min(1),
  requirement: z.string().min(1),
  publishToExternalChannels: z.boolean(),
});

export type JobDesc = z.infer<typeof jobDescSchema>;
console.log("gdagaf");

export async function POST(request: NextRequest) {
  console.log("kjj");
  try {
    const BODY = await request.json();
    console.log(BODY, "body");
    const validatedBody: JobDesc = jobDescSchema.parse(BODY);
    const requestHeaders = new Headers(request.headers);
    const organization = requestHeaders.get("X-Auth-User-Organization")!;
    const user_id = requestHeaders.get("X-Auth-User-Email")!;
    console.log("user_id", user_id);
    console.log(validatedBody, "validatedBody");
    const { data } = await axios.post(`${process.env.NODE_RED_URL}/jobdes`, {
      ...BODY,
      organization,
      created_by: user_id,
    });
    return Response.json({ data });
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { message: "Validation failed", errors: error.issues },
        { status: 400 },
      );
    }
    // Return an error response if an error occurs
    return Response.json({ error });
  }
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const jobdescid = searchParams.get("id");
    const pageNumber = parseInt(searchParams.get("pageNumber") || "1");
    const limit = parseInt(searchParams.get("limit") || "10");
    const status = searchParams.get("Status");
    const organization = request.headers.get("X-Auth-User-Organization");
    const user_email = request.headers.get("X-Auth-User-Email");
    if (jobdescid) {
      const nodeRedApiUrl = `${process.env.NODE_RED_URL}/fetch-item`; // Node-RED URL
      const nodeRedResponse = await axios.get(nodeRedApiUrl, {
        params: {
          jobdescid,
        },
      });

      // Return the Node-RED response
      return new NextResponse(JSON.stringify(nodeRedResponse.data), {
        headers: { "Content-Type": "application/json" },
        status: 200,
      });
    }
    if (!status) {
      return new NextResponse(JSON.stringify({ error: "Status is required" }), {
        headers: { "Content-Type": "application/json" },
        status: 400,
      });
    }

    console.log(status, organization, user_email, "79999");

    // Make a call to the Node-RED API
    const nodeRedApiUrl = `${process.env.NODE_RED_URL}/filterJobDescription`; // Node-RED URL
    const nodeRedResponse = await axios.get(nodeRedApiUrl, {
      params: {
        // pageNumber,
        // limit,
        status,
        organization,
        //user_email,
      },
    });

    // Return the Node-RED response
    return new NextResponse(JSON.stringify(nodeRedResponse.data), {
      headers: { "Content-Type": "application/json" },
      status: 200,
    });
  } catch (error) {
    // Handle errors
    return new NextResponse(JSON.stringify({ error: (error as any).message || 'Unknown error' }), {
      headers: { "Content-Type": "application/json" },
      status: 500,
    });
  }
}
export async function PUT(request: NextRequest) {
  try {
    // Parse the request body as JSON
    const body = await request.json();
    console.log("Parsed request body:", body);

    // Validate the body with your schema
    const validatedBody: JobDesc = jobDescSchema.parse(body);
    console.log("Validated request body:", validatedBody);

    // Extract the user ID from headers
    const user_id = request.headers.get("X-Auth-User-Email");
    const organization = request.headers.get("X-Auth-User-Organization");
    // const user_id = "89781b7e-9511-45bf-8f04-e62e5c2510d5";
    const jobid = body.jobdescid;

    console.log("Extracted user_id:", user_id);

    // Handle missing user ID
    if (!user_id) {
      console.error("User ID is missing from headers");
      return NextResponse.json(
        { message: "User ID missing in the headers." },
        { status: 400 },
      );
    }

    // Log the payload being sent
    console.log("Payload to be sent:", {
      ...validatedBody,
      jobdescid: jobid,
      created_by:user_id,
      organization,
    });

    // Make the PUT request to the external API
    const { data } = await axios.put(`${process.env.NODE_RED_URL}/jobdes`, {
      ...validatedBody,
      created_by:user_id,
      jobdescid: jobid,
      organization,
    });

    console.log("API Response Data:", data);

    // Return the successful response
    return NextResponse.json({ data }, { status: 200 });
  } catch (error) {
    // Handle schema validation errors
    if (error instanceof z.ZodError) {
      console.error("Validation Error:", error.issues);
      return NextResponse.json(
        { message: "Validation failed", errors: error.issues },
        { status: 400 },
      );
    }

    // Handle other errors (e.g., network issues, external API errors)
    if ((error as any).response) {
      console.error("Axios error:", (error as any).response?.data || (error as any).message);
      return NextResponse.json(
        {
          message: "External API error",
          error: (error as any).response?.data || (error as any).message,
        },
        { status: (error as any).response?.status || 500 },
      );
    }

    console.error("Unexpected error:", error);
    return NextResponse.json(
      { message: "An unexpected error occurred", error: (error as any).message || 'Unknown error' },
      { status: 500 },
    );
  }
}

export async function DELETE(request: NextRequest) {
  try {
    const queryParams = new URL(request.url).searchParams;
    const jobdescid = queryParams.get("jobdescid");

    console.log("JOBDescid ID:", jobdescid);
    if (!jobdescid) {
      return NextResponse.json(
        { error: "Missing candidate ID" },
        { status: 400 },
      );
    }
    const response = await axios.delete(
      `${process.env.NODE_RED_URL}/jd-item-delete`,
      {
        params: {
          jobdescid,
        },
      },
    );
    console.log("API Response from Node-RED:", response.data);
    return NextResponse.json({ message: "Deleted successfully" });
  } catch (error) {
    console.error("Error during DELETE:", error);
    return new Response(
      JSON.stringify({ error: "Could not delete candidate" }),
      { status: 500 },
    );
  }
}
