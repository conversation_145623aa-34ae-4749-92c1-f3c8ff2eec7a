import { DynamoDBtable } from "@/lib/db_actions";
import axios from "axios";
import { v4 as uuidv4 } from "uuid";
import { z } from "zod";
import { NextRequest, NextResponse } from "next/server";

//* Create a new DynamoDB table instance for the "interviewdetails" table
const InterviewTable = new DynamoDBtable("interview");
const CandidateTable = new DynamoDBtable("LCPCandidate");

// Define a schema for each level
const levelSchema = z
  .array(z.string())
  .min(1, "Task array must contain at least one item");

// Define the main schema where all keys are optional initially
const interviewSchema = z
  .object({
    level1: levelSchema.optional(),
    level2: levelSchema.optional(),
    level3: levelSchema.optional(),
  })
  .refine((data) => data.level1 || data.level2 || data.level3, {
    message: "At least one of level1, level2, or level3 is required.",
  });

// Example usage
const result = interviewSchema.safeParse({
  level2: ["Task 1"], // This will pass validation
});

console.log(result.success); // true

const result2 = interviewSchema.safeParse({
  level4: ["Task 1"], // This will fail validation since none of level1, level2, or level3 is present
});

console.log(result2.success); // false
export type interviewTableSchema = z.infer<typeof interviewSchema>;
console.log("gdagaf");

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const candidateId = searchParams.get("candidateId");

    if (!candidateId) {
      return NextResponse.json(
        { error: "Candidate ID is required" },
        { status: 400 },
      );
    }

    // Fetch a specific record from the external API
    const { data: record } = await axios.get(
      `${process.env.NODE_RED_URL}/getAIInterviewList`,
      {
        data: { candidate_id: candidateId },
      },
    );

    if (!record) {
      return NextResponse.json({ error: "Record not found" }, { status: 404 });
    }

    return NextResponse.json(record);
  } catch (error) {
    if (axios.isAxiosError(error)) {
      return NextResponse.json(
        {
          message: "Failed to fetch data from external API",
          error: error.message,
        },
        { status: error.response?.status || 500 },
      );
    }
    return NextResponse.json({ error: error }, { status: 500 });
  }
}

export async function POST(req: NextRequest) {
  try {
    const body = await req.json();

    // Validate required fields
    const requiredFields = [
      "candidate_id",
      "start_time",
      "end_time",
      "decision",
      "status",
      "interviewnotes",
      "is_ai_interview",
      "panel",
      "level",
      "jobdescid",
      "summary",
      "timestamp",
      "video"

    ];

    for (const field of requiredFields) {
      if (!(field in body)) {
        return NextResponse.json(
          { error: `${field} is required` },
          { status: 400 },
        );
      }
    }

    let modifiedBody = { ...body };

    try {
      // Fetch previous interview data
      console.log("Fetching interview data for candidate:", body.candidate_id);
      const response = await axios.get(
        `${process.env.NODE_RED_URL}/getAIInterviewList`,
        {
          data: { candidate_id: body.candidate_id },
        },
      );

      const previousData = response.data || [];
      console.log("Previous interview data:", previousData);

      // Sort interviews by timestamp to get the latest decisions
      const sortedInterviews = previousData.sort(
        (a: any, b: any) =>
          new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime(),
      );

      // Find latest previous level details
      const level1Details = sortedInterviews.find(
        (interview: any) => interview.level === "level1",
      );
      const level2Details = sortedInterviews.find(
        (interview: any) => interview.level === "level2",
      );

      console.log("Latest Level 1 details:", level1Details);
      console.log("Latest Level 2 details:", level2Details);
      console.log("Current requested level:", body.level);

      // Handle the case where body.level might be a number
      const requestedLevel = body.level.startsWith("level")
        ? body.level
        : `level${body.level}`;
      modifiedBody.level = requestedLevel;

      if (requestedLevel === "level2") {
        if (!level1Details) {
          return NextResponse.json(
            {
              error:
                "Level 1 interview must be completed before proceeding to Level 2",
            },
            { status: 400 },
          );
        }

        if (!level1Details.decision) {
          return NextResponse.json(
            {
              error:
                "Please provide a decision for Level 1 before proceeding to Level 2",
            },
            { status: 400 },
          );
        }
      }

      if (requestedLevel === "level3") {
        if (!level2Details) {
          return NextResponse.json(
            {
              error:
                "Level 2 interview must be completed before proceeding to Level 3",
            },
            { status: 400 },
          );
        }

        if (!level2Details.decision) {
          return NextResponse.json(
            {
              error:
                "Please provide a decision for Level 2 before proceeding to Level 3",
            },
            { status: 400 },
          );
        }
      }
    } catch (error) {
      console.error("Error fetching previous interview data:", error);
      return NextResponse.json(
        { error: "Failed to validate interview levels" },
        { status: 500 },
      );
    }

    // If all validations pass, proceed with scheduling
    console.log("All validations passed, proceeding with scheduling");
    const scheduleResponse = await fetch(
      `${process.env.NODE_RED_URL}/scheduleInterviewWithAI`,
      {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(modifiedBody),
      },
    );

    const responseData = await scheduleResponse.json();

    if (!scheduleResponse.ok) {
      console.error("Failed to schedule interview:", responseData);
      return NextResponse.json(
        { error: "Failed to save data to external API", details: responseData },
        { status: scheduleResponse.status },
      );
    }

    return NextResponse.json(
      { message: "Interview data saved successfully", data: responseData },
      { status: 201 },
    );
  } catch (error) {
    console.error("Error in POST request:", error);
    return NextResponse.json(
      { error: "Internal Server Error" },
      { status: 500 },
    );
  }
}
 
export async function PUT(req: NextRequest) {
  try {
    const body = await req.json();

    // Validate required fields
    const requiredFields = [
      "candidate_id",
      "start_time",
      "decision",
      "status",
      "interviewnotes",
      "video",
      "level",
    ];

    for (const field of requiredFields) {
      if (!(field in body)) {
        return NextResponse.json(
          { error: `${field} is required` },
          { status: 400 },
        );
      }
    }

    // Put data to external API
    const response = await fetch(
      `${process.env.NODE_RED_URL}/updateInterviewWithAI`,
      {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(body),
      },
    );

    const responseData = await response.json();

    if (!response.ok) {
      return NextResponse.json(
        {
          error: "Failed to update data on external API",
          details: responseData,
        },
        { status: response.status },
      );
    }

    return NextResponse.json(
      { message: "Interview data updated successfully", data: responseData },
      { status: 200 },
    );
  } catch (error) {
    console.error("Error processing request:", error);
    return NextResponse.json(
      { error: "Internal Server Error" },
      { status: 500 },
    );
  }
}
