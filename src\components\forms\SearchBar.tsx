import React from 'react';
import { cn } from '@/lib/utils';
import { Icon, Input } from '@/components/ui';

interface SearchBarProps {
  placeholder?: string;
  value?: string;
  onChange?: (value: string) => void;
  onSearch?: (value: string) => void;
  showFilters?: boolean;
  onFilterClick?: () => void;
  className?: string;
}

const SearchBar: React.FC<SearchBarProps> = ({
  placeholder = 'Search...',
  value = '',
  onChange,
  onSearch,
  showFilters = true,
  onFilterClick,
  className,
}) => {
  const [searchValue, setSearchValue] = React.useState(value);

  React.useEffect(() => {
    setSearchValue(value);
  }, [value]);

  const handleInputChange = (newValue: string) => {
    setSearchValue(newValue);
    onChange?.(newValue);
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      onSearch?.(searchValue);
    }
  };

  const handleSearchClick = () => {
    onSearch?.(searchValue);
  };

  const handleClear = () => {
    setSearchValue('');
    onChange?.('');
    onSearch?.('');
  };

  return (
    <div className={cn('flex items-center space-x-2', className)}>
      <div className="relative flex-1">
        <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
          <Icon name="search" size="sm" className="text-gray-400" />
        </div>
        
        <input
          type="text"
          placeholder={placeholder}
          value={searchValue}
          onChange={(e) => handleInputChange(e.target.value)}
          onKeyPress={handleKeyPress}
          className="block w-full pl-10 pr-12 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
        />
        
        {searchValue && (
          <button
            onClick={handleClear}
            className="absolute inset-y-0 right-8 flex items-center pr-2 text-gray-400 hover:text-gray-600"
          >
            <Icon name="x" size="sm" />
          </button>
        )}
        
        <button
          onClick={handleSearchClick}
          className="absolute inset-y-0 right-0 flex items-center pr-3 text-gray-400 hover:text-blue-600"
        >
          <Icon name="search" size="sm" />
        </button>
      </div>
      
      {showFilters && (
        <button
          onClick={onFilterClick}
          className="inline-flex items-center px-3 py-2 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500"
        >
          <Icon name="filter" size="sm" className="mr-2" />
          Filters
        </button>
      )}
    </div>
  );
};

export default SearchBar;
