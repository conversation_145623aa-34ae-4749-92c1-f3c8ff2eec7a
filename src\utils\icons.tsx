export const SuccessIcon = () => (
  <div
    className="flex h-14 w-14 items-center justify-center rounded-full"
    style={{
      background: "#1EA779",
    }}
  >
    <svg
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M10.9269 2.36021C11.2804 2.57626 11.3917 3.03789 11.1757 3.3913C11.0189 3.64775 11.0582 3.97821 11.2707 4.19075L11.3686 4.28863C11.9572 4.87729 12.1743 5.74206 11.9334 6.53894C11.8136 6.93543 11.395 7.1597 10.9985 7.03985C10.602 6.91999 10.3777 6.50141 10.4976 6.10492C10.5785 5.83734 10.5056 5.54695 10.3079 5.34929L10.2101 5.25141C9.50753 4.54889 9.37765 3.45659 9.89586 2.60892C10.1119 2.25551 10.5735 2.14416 10.9269 2.36021Z"
        fill="white"
      ></path>
      <path
        d="M17.6897 4.7217C18.0959 4.80294 18.3593 5.19806 18.278 5.60423L18.1341 6.32416C17.9359 7.31498 17.222 8.12364 16.2634 8.44317C15.8155 8.59248 15.4819 8.97035 15.3893 9.43333L15.2454 10.1533C15.1641 10.5594 14.769 10.8229 14.3628 10.7416C13.9567 10.6604 13.6932 10.2653 13.7745 9.8591L13.9185 9.13916C14.1166 8.14835 14.8305 7.33968 15.7891 7.02015C16.237 6.87084 16.5706 6.49297 16.6632 6.02999L16.8072 5.31005C16.8884 4.90388 17.2835 4.64047 17.6897 4.7217Z"
        fill="white"
      ></path>
      <path
        fill-rule="evenodd"
        clip-rule="evenodd"
        d="M11.719 8.81308C10.9004 7.99439 10.2258 7.31977 9.63944 6.88505C9.03367 6.43593 8.36045 6.11944 7.58515 6.30247C6.80985 6.48549 6.34924 7.06964 6.00827 7.74226C5.67825 8.39329 5.3766 9.29836 5.01051 10.3967L3.28047 15.5869C2.83712 16.9168 2.47736 17.996 2.32849 18.8329C2.17827 19.6773 2.19343 20.5341 2.82953 21.1702C3.46564 21.8063 4.32249 21.8215 5.1669 21.6713C6.00371 21.5224 7.08289 21.1627 8.41277 20.7193L13.603 18.9893C14.7013 18.6232 15.6065 18.3215 16.2575 17.9915C16.9301 17.6505 17.5143 17.1899 17.6973 16.4146C17.8803 15.6393 17.5638 14.9661 17.1147 14.3603C16.68 13.774 16.0054 13.0994 15.1867 12.2808L11.719 8.81308ZM8.69471 8.05251C8.71148 8.06455 8.7286 8.07704 8.74608 8.09C9.23219 8.45041 9.8285 9.04391 10.7027 9.91808L11.4625 10.6779C11.4474 10.7172 11.4307 10.7616 11.4128 10.8108C11.3357 11.022 11.2344 11.3229 11.1335 11.6908C10.9326 12.4229 10.7276 13.4402 10.7276 14.5555C10.7276 15.6176 10.9335 16.6352 11.1325 17.375C11.2153 17.6831 11.2983 17.9477 11.3676 18.1533L8.73292 19.0315C8.72206 18.9822 8.71004 18.9272 8.69702 18.867C8.63609 18.5853 8.55362 18.1911 8.46757 17.7419C8.29382 16.8349 8.11181 15.7354 8.05608 14.8855C7.96402 13.4817 8.14385 11.6498 8.35648 10.1344C8.46177 9.38405 8.57313 8.72455 8.65812 8.25294C8.67096 8.18169 8.68319 8.11477 8.69471 8.05251ZM6.95481 9.35363C6.79648 9.78402 6.62266 10.3037 6.4137 10.9306L4.7242 15.9991C4.25523 17.406 3.93255 18.3802 3.8053 19.0956C3.67708 19.8164 3.79641 20.0158 3.89019 20.1096C3.98397 20.2033 4.18341 20.3227 4.90418 20.1945C5.49458 20.0894 6.26135 19.8513 7.30217 19.5076C7.28431 19.4281 7.25995 19.3184 7.23091 19.1841C7.16809 18.8936 7.08312 18.4875 6.99436 18.0241C6.81848 17.1059 6.6214 15.9309 6.55929 14.9836C6.45664 13.4182 6.6559 11.4592 6.87103 9.92601C6.89875 9.72851 6.92686 9.53716 6.95481 9.35363ZM12.7906 17.6789L13.0692 17.5861C14.242 17.1951 15.0395 16.9272 15.5793 16.6536C16.1092 16.385 16.2065 16.2009 16.2374 16.07C16.2683 15.939 16.2636 15.7309 15.9098 15.2537C15.5494 14.7676 14.9559 14.1713 14.0817 13.2971L12.6451 11.8605C12.6236 11.9323 12.6018 12.0082 12.58 12.0877C12.4018 12.7372 12.2276 13.6154 12.2276 14.5555C12.2276 15.1741 12.3128 15.7905 12.4257 16.3343C12.4739 16.5663 12.5271 16.7852 12.581 16.9853C12.6554 17.262 12.7297 17.4983 12.7906 17.6789Z"
        fill="white"
      ></path>
      <path
        d="M20.3311 12.73C20.6264 12.4681 21.0471 12.4016 21.4088 12.5598L21.7003 12.6872C22.0798 12.8532 22.522 12.68 22.6879 12.3005C22.8539 11.921 22.6807 11.4788 22.3012 11.3129L22.0097 11.1854C21.1123 10.793 20.0687 10.9579 19.3359 11.6077C19.0097 11.8969 18.535 11.945 18.1575 11.727L17.9446 11.6041C17.5859 11.397 17.1272 11.5199 16.9201 11.8787C16.713 12.2374 16.8359 12.6961 17.1946 12.9032L17.4075 13.0261C18.3441 13.5669 19.5218 13.4476 20.3311 12.73Z"
        fill="white"
      ></path>
      <path
        d="M13.9794 4.058C13.8632 4.09485 13.7626 4.19537 13.5616 4.39643C13.3605 4.59749 13.26 4.69802 13.2231 4.81425C13.192 4.91257 13.192 5.01812 13.2231 5.11644C13.26 5.23268 13.3605 5.3332 13.5616 5.53426C13.7626 5.73532 13.8632 5.83585 13.9794 5.87269C14.0777 5.90386 14.1833 5.90386 14.2816 5.87269C14.3978 5.83585 14.4984 5.73532 14.6994 5.53426C14.9005 5.3332 15.001 5.23268 15.0378 5.11644C15.069 5.01812 15.069 4.91257 15.0378 4.81425C15.001 4.69802 14.9005 4.59749 14.6994 4.39643C14.4984 4.19537 14.3978 4.09485 14.2816 4.058C14.1833 4.02683 14.0777 4.02683 13.9794 4.058Z"
        fill="white"
      ></path>
      <path
        d="M19.4688 7.46833C19.7142 7.22294 19.8369 7.10025 19.9753 7.04692C20.1373 6.9845 20.3167 6.9845 20.4787 7.04692C20.6171 7.10025 20.7398 7.22294 20.9852 7.46833C21.2306 7.71372 21.3533 7.83642 21.4066 7.97483C21.469 8.13681 21.469 8.3162 21.4066 8.47818C21.3533 8.61659 21.2306 8.73929 20.9852 8.98468C20.7398 9.23007 20.6171 9.35277 20.4787 9.4061C20.3167 9.46851 20.1373 9.46851 19.9753 9.4061C19.8369 9.35277 19.7142 9.23007 19.4688 8.98468C19.2234 8.73929 19.1007 8.61659 19.0474 8.47818C18.985 8.3162 18.985 8.13681 19.0474 7.97483C19.1007 7.83642 19.2234 7.71372 19.4688 7.46833Z"
        fill="white"
      ></path>
      <path
        d="M7.68653 3.9409C7.47706 3.73143 7.13744 3.73143 6.92798 3.9409C6.71851 4.15037 6.71851 4.48999 6.92798 4.69945C7.13744 4.90892 7.47706 4.90892 7.68653 4.69945C7.896 4.48999 7.896 4.15037 7.68653 3.9409Z"
        fill="white"
      ></path>
      <path
        d="M19.0588 15.3135C19.2683 15.104 19.6079 15.104 19.8174 15.3135C20.0269 15.5229 20.0269 15.8625 19.8174 16.072C19.6079 16.2815 19.2683 16.2815 19.0588 16.072C18.8494 15.8625 18.8494 15.5229 19.0588 15.3135Z"
        fill="white"
      ></path>
      <path
        d="M18.2592 9.74155C18.0497 9.53208 17.7101 9.53208 17.5006 9.74155C17.2911 9.95102 17.2911 10.2906 17.5006 10.5001C17.7101 10.7096 18.0497 10.7096 18.2592 10.5001C18.4686 10.2906 18.4686 9.95102 18.2592 9.74155Z"
        fill="white"
      ></path>
    </svg>
  </div>
);
