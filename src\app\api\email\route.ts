import { NextResponse } from 'next/server';
import axios from 'axios';

export const runtime = 'nodejs';

interface CandidateData {
  candidateId: string;
  firstName: string;
  email: string;
  jobTitle: string;
  interviewDate?: string;
  interviewLink?: string;
}

export async function POST(request: Request) {
  try {
    const {
      candidateId,
      firstName,
      email,
      jobTitle,
      interviewDate,
      interviewLink,
    }: CandidateData = await request.json();

    const organization = request.headers.get("X-Auth-User-Organization");

    // Validate required fields
    if (!email || !firstName  || !jobTitle || !organization) {
      return NextResponse.json(
        { success: false, error: 'Missing required fields' },
        { status: 400 }
      );
    }

    const sentDate = new Date(); // when the email is being sent
const completionDeadline = new Date(sentDate.getTime() + 2 * 24 * 60 * 60 * 1000); // +2 days
const formattedDeadline = completionDeadline.toLocaleDateString('en-US', {
  weekday: 'short',
  year: 'numeric',
  month: 'short',
  day: 'numeric',
  hour: '2-digit',
  minute: '2-digit'
});


    // Step 1: Get Microsoft Graph API Access Token
    const tokenRes = await axios.post(
      `https://login.microsoftonline.com/${process.env.TENANT_ID}/oauth2/v2.0/token`,
      new URLSearchParams({
        client_id: process.env.CLIENT_ID!,
        client_secret: process.env.CLIENT_SECRET!,
        scope: 'https://graph.microsoft.com/.default',
        grant_type: 'client_credentials',
      }),
      { headers: { 'Content-Type': 'application/x-www-form-urlencoded' } }
    );

    const accessToken: string = tokenRes.data.access_token;

    // Step 2: Build Email Template
    const emailBody = `
     <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
  <h2 style="color: #2563eb;">Congratulations ${firstName}!</h2>

  <p>We're pleased to inform you that you've been selected for the next stage of the hiring process for the position of <strong>${jobTitle}</strong> at ${organization}.</p>

  ${interviewDate ? `
  <div style="background-color: #f3f4f6; padding: 16px; border-radius: 8px; margin: 16px 0;">
    <h3 style="margin-top: 0;">Interview Details</h3>
    <p><strong>Date:</strong> ${interviewDate}</p>
    ${interviewLink ? `<p><strong>Meeting Link:</strong> <a href="${interviewLink}">Join Interview</a></p>` : ''}
    <p style="margin-top: 12px; color: #d97706;"><strong>Note:</strong> Please complete the AI interview within <strong>2 days</strong> of receiving this email (by <strong>${completionDeadline}</strong>).</p>
  </div>
  ` : ''}

  <p>Please reply to this email if you have any questions or need to reschedule.</p>

  <p>Best regards,<br/>
  <strong>The ${organization} Hiring Team</strong></p>

  <div style="margin-top: 32px; font-size: 12px; color: #6b7280;">
    <p>This is an automated message. Please do not reply directly to this email.</p>
    <p>Candidate ID: ${candidateId}</p>
  </div>
</div>

    `;

    // Step 3: Send Email to Candidate
    await axios.post(
      `https://graph.microsoft.com/v1.0/users/${process.env.SENDER_EMAIL}/sendMail`,
      {
        message: {
          subject: `Interview Invitation for ${jobTitle} at ${organization}`,
          body: {
            contentType: 'HTML',
            content: emailBody,
          },
          toRecipients: [
            {
              emailAddress: {
                address: email,
              },
            },
          ],
          // CC to HR team for record keeping
          ccRecipients: process.env.HR_EMAIL ? [
            {
              emailAddress: {
                address: process.env.HR_EMAIL,
              },
            }
          ] : [],
        },
        saveToSentItems: 'true',
      },
      {
        headers: {
          Authorization: `Bearer ${accessToken}`,
          'Content-Type': 'application/json',
        },
      }
    );

    // Step 4: Send Internal Notification
    await axios.post(
      `https://graph.microsoft.com/v1.0/users/${process.env.SENDER_EMAIL}/sendMail`,
      {
        message: {
          subject: `Candidate Selected: ${firstName}  for ${jobTitle}`,
          body: {
            contentType: 'HTML',
            content: `
              <p>The following candidate has been selected and notified:</p>
              <ul>
                <li><strong>Name:</strong> ${firstName} </li>
                <li><strong>Position:</strong> ${jobTitle}</li>
                <li><strong>Candidate ID:</strong> ${candidateId}</li>
                <li><strong>Email:</strong> ${email}</li>
              </ul>
            `,
          },
          toRecipients: [
            {
              emailAddress: {
                address: process.env.HIRING_MANAGER_EMAIL || process.env.SENDER_EMAIL,
              },
            },
          ],
        },
        saveToSentItems: 'true',
      },
      {
        headers: {
          Authorization: `Bearer ${accessToken}`,
          'Content-Type': 'application/json',
        },
      }
    );

    return NextResponse.json(
      { 
        success: true, 
        message: 'Candidate notification email sent successfully!',
        candidateId,
        email
      },
      { status: 200 }
    );
  } catch (err: any) {
    console.error('Email sending error:', {
      error: err.response?.data || err.message,
      stack: err.stack
    });
    
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to send candidate notification',
        details: err.response?.data || err.message
      },
      { status: 500 }
    );
  }
}