'use client';

import React, { useState } from 'react';
import DatePicker from 'react-datepicker';
import { format, addDays, setHours, setMinutes } from 'date-fns';
import { cn } from '@/lib/utils';
import { Icon } from '@/components/ui';
import "react-datepicker/dist/react-datepicker.css";

interface DateTimePickerProps {
  selectedDate: Date | null;
  selectedTime: string;
  onDateChange: (date: Date | null) => void;
  onTimeChange: (time: string) => void;
  minDate?: Date;
  maxDate?: Date;
  disabled?: boolean;
  error?: string;
  className?: string;
}

const DateTimePicker: React.FC<DateTimePickerProps> = ({
  selectedDate,
  selectedTime,
  onDateChange,
  onTimeChange,
  minDate = new Date(),
  maxDate = addDays(new Date(), 90), // 3 months ahead
  disabled = false,
  error,
  className,
}) => {
  // Generate time slots (9 AM to 6 PM, 30-minute intervals)
  const generateTimeSlots = () => {
    const slots = [];
    for (let hour = 9; hour <= 18; hour++) {
      for (let minute = 0; minute < 60; minute += 30) {
        if (hour === 18 && minute > 0) break; // Stop at 6:00 PM
        const time = format(setMinutes(setHours(new Date(), hour), minute), 'HH:mm');
        const displayTime = format(setMinutes(setHours(new Date(), hour), minute), 'h:mm a');
        slots.push({ value: time, label: displayTime });
      }
    }
    return slots;
  };

  const timeSlots = generateTimeSlots();

  // Filter out weekends and past dates
  const isWeekday = (date: Date) => {
    const day = date.getDay();
    return day !== 0 && day !== 6; // 0 = Sunday, 6 = Saturday
  };

  const filterDate = (date: Date) => {
    return isWeekday(date) && date >= minDate && date <= maxDate;
  };

  return (
    <div className={cn('space-y-4', className)}>
      {/* Date Picker */}
      <div className="space-y-2">
        <label className="block text-sm font-medium text-gray-700">
          Interview Date
          <span className="text-red-500 ml-1">*</span>
        </label>
        <div className="relative">
          <DatePicker
            selected={selectedDate}
            onChange={onDateChange}
            filterDate={filterDate}
            minDate={minDate}
            maxDate={maxDate}
            disabled={disabled}
            placeholderText="Select interview date"
            dateFormat="MMMM d, yyyy"
            className={cn(
              'w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 disabled:bg-gray-100 disabled:cursor-not-allowed',
              error && 'border-red-500 focus:ring-red-500 focus:border-red-500'
            )}
            calendarClassName="shadow-lg border border-gray-200 rounded-lg"
            dayClassName={(date) =>
              cn(
                'hover:bg-blue-100 rounded-lg transition-colors',
                !isWeekday(date) && 'text-gray-400 cursor-not-allowed'
              )
            }
            weekDayClassName={() => 'text-gray-600 font-medium'}
            monthClassName={() => 'text-gray-800'}
            timeClassName={() => 'text-gray-600'}
          />
          <Icon 
            name="calendar" 
            size="sm" 
            className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 pointer-events-none" 
          />
        </div>
        <p className="text-xs text-gray-500">
          Only weekdays are available for scheduling
        </p>
      </div>

      {/* Time Picker */}
      <div className="space-y-2">
        <label className="block text-sm font-medium text-gray-700">
          Interview Time
          <span className="text-red-500 ml-1">*</span>
        </label>
        <div className="relative">
          <select
            value={selectedTime}
            onChange={(e) => onTimeChange(e.target.value)}
            disabled={disabled || !selectedDate}
            className={cn(
              'w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 disabled:bg-gray-100 disabled:cursor-not-allowed appearance-none',
              error && 'border-red-500 focus:ring-red-500 focus:border-red-500'
            )}
          >
            <option value="">Select time slot</option>
            {timeSlots.map((slot) => (
              <option key={slot.value} value={slot.value}>
                {slot.label}
              </option>
            ))}
          </select>
          <Icon 
            name="clock" 
            size="sm" 
            className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 pointer-events-none" 
          />
        </div>
        <p className="text-xs text-gray-500">
          Business hours: 9:00 AM - 6:00 PM (30-minute intervals)
        </p>
      </div>

      {/* Timezone Display */}
      <div className="space-y-2">
        <label className="block text-sm font-medium text-gray-700">
          Timezone
        </label>
        <div className="px-3 py-2 bg-gray-50 border border-gray-200 rounded-lg text-sm text-gray-600">
          <Icon name="globe" size="sm" className="inline mr-2" />
          Asia/Kolkata (IST) - Indian Standard Time
        </div>
      </div>

      {/* Selected Date/Time Summary */}
      {selectedDate && selectedTime && (
        <div className="p-3 bg-blue-50 border border-blue-200 rounded-lg">
          <div className="flex items-center space-x-2">
            <Icon name="check" size="sm" className="text-blue-600" />
            <span className="text-sm font-medium text-blue-800">
              Interview Scheduled
            </span>
          </div>
          <p className="text-sm text-blue-700 mt-1">
            {format(selectedDate, 'EEEE, MMMM d, yyyy')} at{' '}
            {timeSlots.find(slot => slot.value === selectedTime)?.label} IST
          </p>
        </div>
      )}

      {/* Error Message */}
      {error && (
        <div className="flex items-center space-x-2 text-red-600">
          <Icon name="alert" size="sm" />
          <span className="text-sm">{error}</span>
        </div>
      )}
    </div>
  );
};

export default DateTimePicker;
