import React from 'react';
import { cn, formatRelativeTime } from '@/lib/utils';
import { Card, Badge, Button, Icon } from '@/components/ui';
import { Candidate } from '@/types';

interface CandidateCardProps {
  candidate: Candidate;
  loading?: boolean;
  onView?: (candidate: Candidate) => void;
  onContact?: (candidate: Candidate) => void;
  onScheduleInterview?: (candidate: Candidate) => void;
  className?: string;
}

const CandidateCard: React.FC<CandidateCardProps> = ({
  loading,
  candidate,
  onView,
  onContact,
  onScheduleInterview,
  className,
}) => {
  const handleView = () => onView?.(candidate);
  const handleContact = () => onContact?.(candidate);
  const handleScheduleInterview = () => onScheduleInterview?.(candidate);

  const getStatusVariant = (status: string) => {
    switch (status) {
      case 'new': return 'info';
      case 'screening': return 'warning';
      case 'interview': return 'default';
      case 'offer': return 'success';
      case 'hired': return 'success';
      case 'rejected': return 'danger';
      default: return 'default';
    }
  };

   if (loading) {
      return (
        <div className={cn('bg-white rounded-lg border border-gray-200', className)}>
          <div className="p-8 text-center">
            <div className="animate-spin inline-block w-6 h-6 border-2 border-blue-600 border-t-transparent rounded-full"></div>
            <p className="mt-2 text-sm text-gray-600">Loading...</p>
          </div>
        </div>
      );
    }

  return (
    <Card className={cn('hover:shadow-md transition-shadow', className)} padding="sm">
      <div className="flex items-start space-x-3 sm:space-x-4">
        {/* Avatar */}
        <div className="flex-shrink-0">
          {candidate.avatar ? (
            <img
              src={candidate.avatar}
              alt={candidate.name}
              className="w-10 h-10 sm:w-12 sm:h-12 rounded-full object-cover"
            />
          ) : (
            <div className="w-10 h-10 sm:w-12 sm:h-12 bg-gray-300 rounded-full flex items-center justify-center">
              <span className="text-gray-600 font-medium text-sm sm:text-lg">
                {candidate.name.split(' ').map(n => n[0]).join('')}
              </span>
            </div>
          )}
        </div>

        {/* Content */}
        <div className="flex-1 min-w-0">
          <div className="flex flex-col sm:flex-row sm:items-start sm:justify-between mb-2 space-y-2 sm:space-y-0">
            <div className="min-w-0 flex-1">
              <h3 className="text-base sm:text-lg font-semibold text-gray-900 truncate">
                {candidate.name}
              </h3>
              <p className="text-xs sm:text-sm text-gray-600 truncate">
                {candidate.email}
              </p>
              {candidate.phone && (
                <p className="text-xs sm:text-sm text-gray-600 truncate">
                  {candidate.phone_number}
                </p>
              )}
            </div>

            <div className="flex-shrink-0">
              <Badge
                variant={getStatusVariant(candidate.status)}
                size="sm"
              >
                {candidate.status}
              </Badge>
            </div>
          </div>

          <div className="space-y-2 mb-3 sm:mb-4">
            <div className="flex flex-col sm:flex-row sm:items-center text-xs sm:text-sm text-gray-600 space-y-1 sm:space-y-0">
              <div className="flex items-center">
                <Icon name="location" size="sm" className="mr-1 flex-shrink-0" />
                <span className="truncate">Trichy</span>
              </div>
              <span className="hidden sm:inline mx-2">•</span>
              <span className="flex-shrink-0">{candidate.years_of_experience} years experience</span>
            </div>

            {candidate?.key_skills?.length  > 0 && (
              <div className="flex flex-wrap gap-1 overflow-hidden">
                {candidate?.key_skills.slice(0, 2).map((skill, index) => (
                  <Badge key={index} variant="default" size="sm" className="text-xs">
                    {skill}
                  </Badge>
                ))}
                {candidate?.key_skills.length > 2 && (
                  <Badge variant="default" size="sm" className="text-xs">
                    +{candidate?.key_skills.length - 2} more
                  </Badge>
                )}
              </div>
            )}
          </div>

          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-2 sm:space-y-0">
            <span className="text-xs sm:text-sm text-gray-500 truncate">
              Applied {formatRelativeTime(new Date(candidate.created_on))}

            </span>

            <div className="flex flex-col sm:flex-row items-stretch sm:items-center space-y-1 sm:space-y-0 sm:space-x-2">
              <Button
                variant="outline"
                size="sm"
                onClick={handleView}
                className="w-full sm:w-auto text-xs sm:text-sm"
              >
                <Icon name="eye" size="sm" className="mr-1" />
                View
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={handleContact}
                className="w-full sm:w-auto text-xs sm:text-sm"
              >
                <Icon name="mail" size="sm" className="mr-1" />
                Contact
              </Button>
              {candidate.status === 'screening' && (
                <Button
                  variant="primary"
                  size="sm"
                  onClick={handleScheduleInterview}
                  className="w-full sm:w-auto text-xs sm:text-sm"
                >
                  <Icon name="calendar" size="sm" className="mr-1" />
                  Interview
                </Button>
              )}
            </div>
          </div>
        </div>
      </div>
    </Card>
  );
};

export default CandidateCard;
