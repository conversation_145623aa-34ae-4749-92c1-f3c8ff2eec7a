// ✨ Multi Resume Upload API ✨
import axios from "axios";
import { NextRequest, NextResponse } from "next/server";

/**
 * <PERSON>les multi resume upload
 * @param {NextRequest} request
 * @returns {NextResponse}
 */
export async function POST(request: NextRequest): Promise<NextResponse> {
  try {
    // Get the form data from the request
    const formData = await request.formData();
    const files = formData.getAll("files");
    const Job_Id = formData.get("jobId");
    // const Job_Id = "c28efefa-3042-4486-a2eb-8d0c71e64ced";
    // const JobDesc = "Merchant";
    const JobDesc = formData.get("JobDesc");

    // Check if files is a FileList and convert it to an array of File
    if (!files) {
      return NextResponse.json({ error: "No files provided" }, { status: 400 });
    }

    // Check if Job_Id and JobDesc are provided
    if (!Job_Id || !JobDesc) {
      return NextResponse.json(
        { error: "No Job_Id or JobDesc provided" },
        { status: 400 },
      );
    }

    // Send the files, Job_Id, and JobDesc to the Node-RED API
    const ApiResponse = await axios.post(
      `${process.env.NODE_RED_URL}/multi-resume-upload`,
      formData,
      {
        headers: {
          "Content-Type": "multipart/form-data",
          context_user: request.headers.get("X-Auth-User-Email")!,
        },
      },
    );
    console.log(ApiResponse, "<<<<<<<<<< ApiResponse >>>>>>>>>>");
    return NextResponse.json(ApiResponse.data, { status: 200 });
  } catch (error) {
    console.log(error, "<<<<<<<<<< error >>>>>>>>>>");
    return NextResponse.json(
      { error: "Internal Server Error" },
      { status: 500 },
    );
  }
}
