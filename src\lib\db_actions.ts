import {
  AttributeMap,
  DeleteItemInput,
  DocumentClient,
  ExpressionAttributeNameMap,
  ExpressionAttributeValueMap,
  GetItemInput,
  ItemList,
  PutItemInput,
  ScanInput,
  ScanOutput,
  UpdateItemInput,
} from "aws-sdk/clients/dynamodb";
import dynamodb from "./dynamodb";
import { Record } from "aws-sdk/clients/machinelearning";

interface PaginationResult {
  items: ItemList | undefined;
  lastEvaluatedKey?: DocumentClient.Key;
  pageCount: Number;
}
interface FilterParams {
  FilterExpression?: string;
  ExpressionAttributeNames?: { [key: string]: string };
  ExpressionAttributeValues?: { [key: string]: any };
}

export class DynamoDBtable {
  put(employeeData: { email: any; address: any; phone: any; role: any[]; }) {
    throw new Error("Method not implemented.");
  }
  private tableName: string;

  constructor(tableName: string) {
    this.tableName = tableName;
  }

  private async paginatedScan(
    filterParams: FilterParams,
    pageNumber: number,
    limit: number,
  ): Promise<PaginationResult> {
    try {
      let exclusiveStartKey: DocumentClient.Key | undefined = undefined;
      let scannedItems: ItemList = [];
      let totalCount = 0; // Initialize total item count

      while (scannedItems.length < limit * pageNumber) {
        const params: ScanInput = {
          TableName: this.tableName,
          ExclusiveStartKey: exclusiveStartKey,
          ...filterParams,
        };

        const result: ScanOutput = await dynamodb.scan(params).promise();

        if (result.Items) {
          scannedItems = scannedItems.concat(result.Items);
        }
        totalCount += result.Items?.length || 0; // Increment totalCount by the number of items scanned
        if (!result.LastEvaluatedKey) {
          break; // No more items to scan
        }

        exclusiveStartKey = result.LastEvaluatedKey;
      }
      const startIdx = (pageNumber - 1) * limit;
      const endIdx = startIdx + limit;
      const paginatedItems = scannedItems.slice(startIdx, endIdx);
      const pageCount = Math.ceil(totalCount / limit);
      return {
        items: paginatedItems,
        lastEvaluatedKey: exclusiveStartKey,
        pageCount,
      };
    } catch (error) {
      console.error(error, "74 --------");
      return {
        items: [],
        lastEvaluatedKey: undefined,
        pageCount: 0,
      };
    }
  }

  /**
   * Creates a new record in the DynamoDB table.
   * @param record - The record to be created, strongly typed as any.
   * @returns Promise<void>
   */
  async create(record: { [key: string]: any }): Promise<void> {
    const params: PutItemInput = {
      TableName: this.tableName,
      Item: record,
    };
    await dynamodb.put(params).promise();
  }

  /**
   * Retrieves a record from the DynamoDB table.
   * @param key - The key of the record to retrieve, strongly typed as any.
   * @returns Promise<Record | undefined> - The retrieved record, or undefined if not found.
   */
  async getAll(
    pageNumber: number = 1,
    limit: number = 10,
  ): Promise<PaginationResult> {
    return this.paginatedScan({}, pageNumber, limit);
  }

  /**
   * Retrieves a single record from the DynamoDB table using the provided key
   * and optional filters.
   * @param key - The key of the record to retrieve.
   * @param filterParams - Optional filter parameters for the query.
   * @returns Promise<Record | undefined> - The retrieved record, or undefined if not found.
   */
  /**
   * Retrieves records from the DynamoDB table using the provided key
   * and optional filters.
   * @param key - The key of the record to retrieve.
   * @param filterParams - Optional filter parameters for the scan.
   * @returns Promise<ItemList | undefined> - The retrieved records, or undefined if not found.
   */
  async scan(
    filterParams: {
      FilterExpression?: string;
      ExpressionAttributeNames?: { [key: string]: string };
      ExpressionAttributeValues?: { [key: string]: any };
    },
    pageNumber: number = 1,
    limit: number = 3,
  ): Promise<PaginationResult> {
    // Create the parameters for the scan operation
    return this.paginatedScan(filterParams, pageNumber, limit);
  }
  async getOne(key: { [key: string]: any }): Promise<AttributeMap | undefined> {
    // Create the parameters for the scan operation
    const params: GetItemInput = {
      TableName: this.tableName,
      Key: key,
    };

    // Perform the scan operation
    const result = await dynamodb.get(params).promise();
    return result.Item;
  }

  /**
   * Updates a record in the DynamoDB table.
   * @param key - The key of the record to update, strongly typed as any.
   * @param updatedFields - The fields to be updated, strongly typed as any.
   * @returns Promise<void>
   */
  async update(
    key: { [key: string]: any },
    Query: string,
    AttValue: ExpressionAttributeValueMap,
    AttributeNames?: { [key: string]: string },
  ): Promise<void> {
    // Create the parameters for the update operation
    let params: UpdateItemInput = {
      TableName: this.tableName,
      Key: key,
      // Specify the fields to be updated and their new values
      UpdateExpression: Query,
      ExpressionAttributeValues: AttValue,
      ReturnValues: "UPDATED_NEW",
    };
    if (AttributeNames) {
      params.ExpressionAttributeNames = AttributeNames;
    }
    // Perform the update operation
    await dynamodb.update(params).promise();
  }

  /**
   * Deletes a record from the DynamoDB table.
   * @param key - The key of the record to delete, strongly typed as any.
   * @returns Promise<void>
   */
  async delete(key: { [key: string]: any }): Promise<void> {
    // Create the parameters for the delete operation
    const params: DeleteItemInput = {
      TableName: this.tableName,
      Key: key,
    };
    // Perform the delete operation
    await dynamodb.delete(params).promise();
  }
}

// private async paginatedScan(
//   filterParams: {
//     FilterExpression?: string;
//     ExpressionAttributeNames?: { [key: string]: string };
//     ExpressionAttributeValues?: { [key: string]: any };
//   },
//   pageNumber: number,
//   limit: number,
// ): Promise<PaginationResult> {
//   try {
//     let exclusiveStartKey: DocumentClient.Key | undefined = undefined;
//     let currentPage = 0;

//     while (currentPage < pageNumber) {
//       const params: ScanInput = {
//         TableName: this.tableName,
//         Limit: limit,
//         ExclusiveStartKey: exclusiveStartKey,
//         ...filterParams,
//       };

//       const result: ScanOutput = await dynamodb.scan(params).promise();
//       console.log(result.Items, "55555");

//       if (!result.LastEvaluatedKey) {
//         throw new Error("Page number out of range");
//       }

//       exclusiveStartKey = result.LastEvaluatedKey;
//       currentPage++;
//     }

//     const params: ScanInput = {
//       TableName: this.tableName,
//       Limit: limit,
//       ExclusiveStartKey: exclusiveStartKey,
//       ...filterParams,
//     };

//     const result: ScanOutput = await dynamodb.scan(params).promise();

//     return {
//       items: result.Items,
//       lastEvaluatedKey: result.LastEvaluatedKey,
//     };
//   } catch (error) {
//     return {
//       items: [],
//       lastEvaluatedKey: undefined,
//     };
//   }
// }
