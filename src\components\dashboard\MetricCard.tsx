import React from 'react';
import { cn } from '@/lib/utils';
import { Card, Icon } from '@/components/ui';
import { DashboardMetric } from '@/types';

interface MetricCardProps {
  metric: DashboardMetric;
  className?: string;
}

const MetricCard: React.FC<MetricCardProps> = ({ metric, className }) => {
  const getChangeColor = () => {
    if (!metric.change) return '';
    return metric.changeType === 'increase' ? 'text-green-600' : 'text-red-600';
  };

  const getChangeIcon = () => {
    if (!metric.change) return null;
    return metric.changeType === 'increase' ? '↗' : '↘';
  };

  const getCardColor = () => {
    const colors = {
      blue: 'border-l-blue-500',
      green: 'border-l-green-500',
      orange: 'border-l-orange-500',
      red: 'border-l-red-500',
      purple: 'border-l-purple-500',
    };
    return metric.color ? colors[metric.color] : 'border-l-blue-500';
  };

  return (
    <Card 
      className={cn(
        'border-l-4 hover:shadow-md transition-shadow',
        getCardColor(),
        className
      )}
      padding="md"
    >
      <div className="flex items-center justify-between">
        <div className="flex-1">
          <p className="text-sm font-medium text-gray-600 mb-1">
            {metric.title}
          </p>
          <p className="text-3xl font-bold text-gray-900">
            {metric.value}
          </p>
          {metric.change && (
            <div className={cn('flex items-center mt-2 text-sm', getChangeColor())}>
              <span className="mr-1">{getChangeIcon()}</span>
              <span>{Math.abs(metric.change)}%</span>
              <span className="text-gray-500 ml-1">vs last month</span>
            </div>
          )}
        </div>
        
        {metric.icon && (
          <div className={cn(
            'p-3 rounded-lg',
            metric.color === 'blue' && 'bg-blue-100 text-blue-600',
            metric.color === 'green' && 'bg-green-100 text-green-600',
            metric.color === 'orange' && 'bg-orange-100 text-orange-600',
            metric.color === 'red' && 'bg-red-100 text-red-600',
            metric.color === 'purple' && 'bg-purple-100 text-purple-600',
            !metric.color && 'bg-blue-100 text-blue-600'
          )}>
            <Icon name={metric.icon} size="lg" />
          </div>
        )}
      </div>
    </Card>
  );
};

export default MetricCard;
