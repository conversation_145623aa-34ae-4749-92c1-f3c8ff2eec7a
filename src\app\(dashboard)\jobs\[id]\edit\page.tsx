'use client';

import React, { useState, useEffect } from 'react';
import { useRouter, useParams } from 'next/navigation';
import JobPostingForm, { JobFormData } from '@/components/forms/JobPostingForm';
import { Icon, Button } from '@/components';

// Interface for job data from API
interface ApiJobData {
  jobdescid: string;
  jobtitle: string;
  responsibilities: string;
  requirement: string;
  skills: string;
  location: string;
  status: string;
  publishToExternalChannels: boolean;
  created_by: string;
  organization?: string;
  createdAt?: string;
  updatedAt?: string;
}

export default function EditJobPage() {
  const router = useRouter();
  const params = useParams();
  const jobId = params.id as string;
  
  const [loading, setLoading] = useState(false);
  const [fetchLoading, setFetchLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [initialData, setInitialData] = useState<JobFormData | null>(null);

  // Fetch job details for editing
  const fetchJobDetails = async () => {
    try {
      setFetchLoading(true);
      setError(null);
      
      const response = await fetch(`/api/jobDescription?id=${jobId}`);

      if (!response.ok) {
        throw new Error('Failed to fetch job details');
      }

      const apiJob: ApiJobData = await response.json();
      
      // Convert API data to form data
      const formData: JobFormData = {
        jobtitle: apiJob.jobtitle,
        responsibilities: apiJob.responsibilities,
        requirement: apiJob.requirement,
        skills: apiJob.skills,
        location: apiJob.location,
        status: apiJob.status,
        publishToExternalChannels: apiJob.publishToExternalChannels,
        department: 'General', // Default since not in API
        jobType: 'full-time', // Default since not in API
        salaryMin: '',
        salaryMax: '',
        salaryCurrency: 'USD',
        closingDate: '',
      };
      
      setInitialData(formData);
    } catch (err) {
      console.error('Error fetching job details:', err);
      setError(err instanceof Error ? err.message : 'Failed to fetch job details');
    } finally {
      setFetchLoading(false);
    }
  };

  useEffect(() => {
    if (jobId) {
      fetchJobDetails();
    }
  }, [jobId]);

  const handleSubmit = async (formData: JobFormData) => {
    setLoading(true);
    setError(null);

    try {
      // Prepare the data for the API call
      const jobData = {
        jobdescid: jobId,
        jobtitle: formData.jobtitle,
        responsibilities: formData.responsibilities,
        requirement: formData.requirement,
        skills: formData.skills,
        location: formData.location,
        status: formData.status,
        publishToExternalChannels: formData.publishToExternalChannels,
        created_by: '',
      };

      const response = await fetch('/api/jobDescription', {
        method: 'PUT',
        body: JSON.stringify(jobData),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to update job posting');
      }

      const result = await response.json();
      console.log('Job updated successfully:', result);

      // Show success message and redirect
      alert('Job updated successfully!');
      router.push(`/jobs/${jobId}`);
    } catch (error: any) {
      console.error('Error updating job:', error);
      
      // Handle different types of errors
      let errorMessage = 'An unexpected error occurred';
      
      if (error.message) {
        errorMessage = error.message;
      }
      
      setError(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  const handleCancel = () => {
    router.push(`/jobs/${jobId}`);
  };

  // Loading state while fetching job details
  if (fetchLoading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center space-x-4">
          <Button variant="ghost" onClick={() => router.back()}>
            <Icon name="arrow-left" size="sm" className="mr-2" />
            Back
          </Button>
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Edit Job Posting</h1>
            <p className="mt-1 text-sm text-gray-600">
              Loading job details...
            </p>
          </div>
        </div>
        
        <div className="flex items-center justify-center py-12">
          <div className="flex items-center space-x-2">
            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
            <span className="text-gray-600">Loading job details...</span>
          </div>
        </div>
      </div>
    );
  }

  // Error state
  if (error && !initialData) {
    return (
      <div className="space-y-6">
        <div className="flex items-center space-x-4">
          <Button variant="ghost" onClick={() => router.back()}>
            <Icon name="arrow-left" size="sm" className="mr-2" />
            Back
          </Button>
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Edit Job Posting</h1>
            <p className="mt-1 text-sm text-gray-600">
              Error loading job details
            </p>
          </div>
        </div>
        
        <div className="bg-red-50 border border-red-200 rounded-md p-4">
          <div className="flex">
            <div className="flex-shrink-0">
              <Icon name="alert" size="sm" className="text-red-400" />
            </div>
            <div className="ml-3">
              <h3 className="text-sm font-medium text-red-800">
                Error loading job details
              </h3>
              <div className="mt-2 text-sm text-red-700">
                <p>{error}</p>
              </div>
              <div className="mt-4">
                <Button variant="outline" onClick={fetchJobDetails}>
                  Try Again
                </Button>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex items-center space-x-4">
        <Button variant="ghost" onClick={() => router.back()}>
          <Icon name="arrow-left" size="sm" className="mr-2" />
          Back
        </Button>
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Edit Job Posting</h1>
          <p className="mt-1 text-sm text-gray-600">
            Update the job posting details and requirements.
          </p>
        </div>
      </div>

      {/* Error Message */}
      {error && (
        <div className="bg-red-50 border border-red-200 rounded-md p-4">
          <div className="flex">
            <div className="flex-shrink-0">
              <Icon name="alert" size="sm" className="text-red-400" />
            </div>
            <div className="ml-3">
              <h3 className="text-sm font-medium text-red-800">
                Error updating job posting
              </h3>
              <div className="mt-2 text-sm text-red-700">
                <p>{error}</p>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Job Posting Form */}
      {initialData && (
        <JobPostingForm
          initialData={initialData}
          onSubmit={handleSubmit}
          onCancel={handleCancel}
          loading={loading}
          isEditing={true}
        />
      )}
    </div>
  );
}
