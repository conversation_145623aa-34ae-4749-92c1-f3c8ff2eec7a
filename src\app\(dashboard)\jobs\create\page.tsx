'use client';

import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import JobPostingForm, { JobFormData } from '@/components/forms/JobPostingForm';

export default function CreateJobPage() {
  const router = useRouter();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleSubmit = async (formData: JobFormData) => {
    setLoading(true);
    setError(null);

    try {
      // Prepare the data for the API call
      const jobData = {
        jobtitle: formData.jobtitle,
        responsibilities: formData.responsibilities,
        requirement: formData.requirement,
        skills: formData.skills,
        location: formData.location,
        status: formData.status,
        publishToExternalChannels: formData.publishToExternalChannels,
        created_by: '',
      };

      const response = await fetch('/api/jobDescription', {
        method: 'POST',
        body: JSON.stringify(jobData),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to create job posting');
      }

      const result = await response.json();
      console.log('Job created successfully:', result);

      // Dispatch event to refresh jobs count in sidebar
      window.dispatchEvent(new CustomEvent('jobCreated'));

      // Show success message and redirect
      alert('Job posted successfully!');
      router.push('/jobs?refresh=true');
    } catch (error: any) {
      console.error('Error creating job:', error);

      // Handle different types of errors
      let errorMessage = 'An unexpected error occurred';

      if (error.message) {
        errorMessage = error.message;
      }

      setError(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  const handleCancel = () => {
    router.back();
  };

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div>
        <h1 className="text-2xl font-bold text-gray-900">Create New Job Posting</h1>
        <p className="mt-1 text-sm text-gray-600">
          Fill out the form below to create a new job posting and start attracting candidates.
        </p>
      </div>

      {/* Error Message */}
      {error && (
        <div className="bg-red-50 border border-red-200 rounded-md p-4">
          <div className="flex">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="ml-3">
              <h3 className="text-sm font-medium text-red-800">
                Error creating job posting
              </h3>
              <div className="mt-2 text-sm text-red-700">
                <p>{error}</p>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Job Posting Form */}
      <JobPostingForm
        onSubmit={handleSubmit}
        onCancel={handleCancel}
        loading={loading}
      />
    </div>
  );
}
