'use client';

import React, { useState, useEffect } from 'react';
import { useRouter, useParams } from 'next/navigation';
import { <PERSON><PERSON>, Card, Badge, Icon, FormField } from '@/components';
import { formatDateConsistent } from '@/lib/utils';

// Interface for candidate data from API
interface ApiCandidateData {
  candidateid: string;
  name: string;
  email: string;
  phone_number?: string;
  years_of_experience: string;
  experience_summary: string;
  achievements: string[];
  certifications: string[];
  education: Array<{
    degree: string;
    start_date: string;
    end_date: string;
    field_of_study: string;
    institution?: string;
  }>;
  employment_history: Array<{
    role: string;
    organization: string;
    start_date: string;
    end_date: string;
  }>;
  key_skills: string[];
  current_location: string;
  flexible_with_relocation: string;
  current_notice_period: string;
  current_ctc: string;
  expected_ctc: string;
  reason_for_change?: string;
  fileid: string;
  status: string;
  created_on: string;
  Job_id?: string;
  interview_eval_details?: any;
}

// Interview level definitions
const INTERVIEW_LEVELS = [
  { id: 1, name: 'AI Interview', description: 'Automated AI screening', type: 'automated' },
  { id: 2, name: 'Technical Interview', description: 'Technical assessment with interviewer', type: 'manual' },
  { id: 3, name: 'Manager Round', description: 'Management interview', type: 'manual' },
  { id: 4, name: 'HR Round', description: 'HR final interview', type: 'manual' }
];

export default function CandidateDetailsPage() {
  const router = useRouter();
  const params = useParams();
  const candidateId = params.id as string;
  
  const [candidate, setCandidate] = useState<ApiCandidateData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [currentLevel, setCurrentLevel] = useState<number>(1);
  const [selectedInterviewer, setSelectedInterviewer] = useState<string>('');
  const [interviewers, setInterviewers] = useState<Array<{name: string}>>([]);
  const [interviewersLoading, setInterviewersLoading] = useState(false);
  const [levelUpdateLoading, setLevelUpdateLoading] = useState(false);
  const [showResumePreview, setShowResumePreview] = useState(false);

  // Fetch candidate details
  const fetchCandidateDetails = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const response = await fetch(`/api/candidate?candidateid=${candidateId}`);
      const responseLevel = await fetch(`/api/interview?candidateId=${candidateId}`);

      if (!response.ok) {
        throw new Error('Failed to fetch candidate details');
      }

      const data = await response.json();
      const leveldata = await responseLevel.json();
      setCandidate(data);

      // Determine current interview level based on status or interview history

      console.log(leveldata[0], "<<<<<<< responseLevel Data >>>>>>");
      
      const level = determineCurrentLevel(leveldata);

      console.log(level, "<<<<<<< Current Level >>>>>>");
      setCurrentLevel(level);

      // Reset interviewer selection when level changes
      setSelectedInterviewer('');
      
    } catch (err) {
      console.error('Error fetching candidate details:', err);
      setError(err instanceof Error ? err.message : 'Failed to fetch candidate details');
    } finally {
      setLoading(false);
    }
  };

  // Fetch interviewers for Level 2
  const fetchInterviewers = async () => {
    try {
      setInterviewersLoading(true);
      const response = await fetch('/api/auth/user-by-role');
      if (response.ok) {
        const data = await response.json();
        setInterviewers(data.data || []);
      } else {
        console.error('Failed to fetch interviewers');
      }
    } catch (err) {
      console.error('Error fetching interviewers:', err);
    } finally {
      setInterviewersLoading(false);
    }
  };

  // Determine current interview level based on candidate data
const determineCurrentLevel = (candidateData: any[]): number => {
  if (!Array.isArray(candidateData)) return 1;

  const levelStatusMap = new Map<number, string>();

  candidateData.forEach(interview => {
    const levelNum = parseInt(interview.level?.replace('level', '') || '0');
    if (levelNum > 0) {
      levelStatusMap.set(levelNum, interview.status);
    }
  });

  // Find highest completed level
  const completedLevels = [...levelStatusMap.entries()]
    .filter(([_, status]) => status === 'Completed')
    .map(([level]) => level);

  const maxCompleted = completedLevels.length > 0 ? Math.max(...completedLevels) : 0;

  // If a scheduled interview exists, and it's for levelX, then previous level is completed
  const scheduledLevels = [...levelStatusMap.entries()]
    .filter(([_, status]) => status === 'Scheduled')
    .map(([level]) => level);

  if (scheduledLevels.length > 0) {
    const minScheduled = Math.min(...scheduledLevels);
    return minScheduled; // Because it's the next to be attempted
  }

  return maxCompleted + 1 || 1;
};




  useEffect(() => {
    if (candidateId) {
      fetchCandidateDetails();
      fetchInterviewers();
    }
  }, [candidateId]);

  const handleLevelProgression = async (newLevel: number) => {
    if (newLevel === 2 && !selectedInterviewer) {
      alert('Please select an interviewer for the Technical Interview');
      return;
    }

    try {
      setLevelUpdateLoading(true);

      // Create interview record for the new level
      const interviewData = {
        candidate_id: candidateId,
        level: `level${newLevel}`,
        status: 'scheduled',
        interviewer: newLevel === 2 ? selectedInterviewer : null,
        start_time: new Date().toISOString(),
        end_time: null,
        decision: 'pending',
        interviewnotes: '',
        is_ai_interview: newLevel === 1,
        panel: newLevel === 2 ? selectedInterviewer : 'System',
        jobdescid: candidate?.Job_id || '',
        summary: `Level ${newLevel} interview scheduled`,
        timestamp: new Date().toISOString(),
        video: null
      };

      // Post to interview API
      const response = await fetch('/api/interview', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(interviewData),
      });

      if (!response.ok) {
        throw new Error('Failed to create interview record');
      }

      setCurrentLevel(newLevel);

      // Refresh candidate data to show updated interview history
      await fetchCandidateDetails();

      alert(`Successfully progressed candidate to Level ${newLevel}`);

    } catch (err) {
      console.error('Error updating interview level:', err);
      alert('Failed to update interview level. Please try again.');
    } finally {
      setLevelUpdateLoading(false);
    }
  };

  const handleLevelChange = async (targetLevel: number) => {
    if (targetLevel === currentLevel) return;

    const confirmMessage = targetLevel > currentLevel
      ? `Are you sure you want to progress to Level ${targetLevel}?`
      : `Are you sure you want to go back to Level ${targetLevel}?`;

    if (!confirm(confirmMessage)) return;

    try {
      setLevelUpdateLoading(true);
      setCurrentLevel(targetLevel);

      // Here you could add API call to update the candidate's current level in the database
      console.log(`Changed candidate level to ${targetLevel}`);

      alert(`Successfully changed level to ${targetLevel}`);

    } catch (err) {
      console.error('Error changing level:', err);
      alert('Failed to change level. Please try again.');
    } finally {
      setLevelUpdateLoading(false);
    }
  };

  const getStatusVariant = (status: string) => {
    switch (status.toLowerCase()) {
      case 'hired': return 'success';
      case 'offer': return 'success';
      case 'interview': return 'warning';
      case 'screening': return 'warning';
      case 'rejected': return 'danger';
      default: return 'default';
    }
  };

  const getLevelStatusVariant = (levelId: number) => {
    if (levelId < currentLevel) return 'success';
    if (levelId === currentLevel) return 'warning';
    return 'default';
  };

  // Loading state
  if (loading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center space-x-4">
          <Button variant="ghost" onClick={() => router.back()}>
            <Icon name="arrow-left" size="sm" className="mr-2" />
            Back
          </Button>
        </div>
        
        <div className="flex items-center justify-center py-12">
          <div className="flex items-center space-x-2">
            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
            <span className="text-gray-600">Loading candidate details...</span>
          </div>
        </div>
      </div>
    );
  }

  // Error state
  if (error || !candidate) {
    return (
      <div className="space-y-6">
        <div className="flex items-center space-x-4">
          <Button variant="ghost" onClick={() => router.back()}>
            <Icon name="arrow-left" size="sm" className="mr-2" />
            Back
          </Button>
        </div>
        
        <Card padding="lg">
          <div className="text-center py-8">
            <Icon name="alert" size="xl" className="text-red-500 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">Candidate Not Found</h3>
            <p className="text-gray-600 mb-4">
              {error || 'The candidate you are looking for does not exist or has been removed.'}
            </p>
            <div className="space-x-4">
              <Button variant="outline" onClick={() => router.back()}>
                Go Back
              </Button>
              <Button variant="primary" onClick={() => router.push('/candidates')}>
                View All Candidates
              </Button>
            </div>
          </div>
        </Card>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button variant="ghost" onClick={() => router.back()}>
            <Icon name="arrow-left" size="sm" className="mr-2" />
            Back
          </Button>
          <div>
            <h1 className="text-2xl font-bold text-gray-900">{candidate.name}</h1>
            <p className="text-gray-600">{candidate.email} • {candidate.current_location}</p>
          </div>
        </div>
        
        <div className="flex items-center space-x-3">
          <Badge variant={getStatusVariant(candidate.status)} size="lg">
            {candidate.status.charAt(0).toUpperCase() + candidate.status.slice(1)}
          </Badge>
        </div>
      </div>

      {/* Main Content Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Left Column - Main Content */}
        <div className="lg:col-span-2 space-y-6">
          {/* Interview Level Management */}
          <Card title="Interview Progress" padding="lg">
            {/* Level Management Controls */}
            <div className="mb-6 p-4 bg-gray-50 rounded-lg">
              <div className="flex items-center justify-between mb-3">
                <span className="text-sm font-medium text-gray-700">Quick Level Management</span>
                <div className="flex space-x-2">
                  {INTERVIEW_LEVELS.map((level) => (
                    <Button
                      key={level.id}
                      variant={level.id === currentLevel ? "primary" : "outline"}
                      size="sm"
                      onClick={() => handleLevelChange(level.id)}
                      disabled={levelUpdateLoading}
                      className="min-w-16"
                    >
                      L{level.id}
                    </Button>
                  ))}
                </div>
              </div>
              <p className="text-xs text-gray-600">
                Click on a level to jump directly to that stage. Current: Level {currentLevel}
              </p>
            </div>

            {/* Progress Bar */}
            <div className="mb-6">
              <div className="flex items-center justify-between mb-2">
                <span className="text-sm font-medium text-gray-700">Overall Progress</span>
                <span className="text-sm text-gray-500">{Math.round(((currentLevel - 1) / 3) * 100)}% Complete</span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div
                  className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                  style={{ width: `${((currentLevel - 1) / 3) * 100}%` }}
                ></div>
              </div>
            </div>

            <div className="space-y-4">
              {INTERVIEW_LEVELS.map((level, index) => {
                const isCompleted = level.id < currentLevel;
                const isCurrent = level.id === currentLevel;
                const isPending = level.id > currentLevel;

                return (
                  <div
                    key={level.id}
                    className={`relative p-4 rounded-lg border-2 transition-all duration-200 ${
                      isCurrent
                        ? 'border-blue-500 bg-blue-50 shadow-md'
                        : isCompleted
                        ? 'border-green-500 bg-green-50'
                        : 'border-gray-200 bg-gray-50'
                    }`}
                  >
                    {/* Connection line to next level */}
                    {index < INTERVIEW_LEVELS.length - 1 && (
                      <div className={`absolute left-6 top-16 w-0.5 h-4 ${
                        isCompleted ? 'bg-green-500' : 'bg-gray-300'
                      }`}></div>
                    )}

                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-4">
                        {/* Status Icon */}
                        <div className={`w-8 h-8 rounded-full flex items-center justify-center ${
                          isCompleted
                            ? 'bg-green-500 text-white'
                            : isCurrent
                            ? 'bg-blue-500 text-white'
                            : 'bg-gray-300 text-gray-600'
                        }`}>
                          {isCompleted ? (
                            <Icon name="check" size="sm" />
                          ) : (
                            <span className="text-sm font-bold">{level.id}</span>
                          )}
                        </div>

                        <div className="flex-1">
                          <div className="flex items-center space-x-2">
                            <h4 className="font-medium text-gray-900">{level.name}</h4>
                            <Badge variant={getLevelStatusVariant(level.id)} size="sm">
                              {isCompleted ? 'Completed' : isCurrent ? 'Current' : 'Pending'}
                            </Badge>
                          </div>
                          <p className="text-sm text-gray-600">{level.description}</p>
                          {level.type === 'automated' && (
                            <p className="text-xs text-blue-600 mt-1">• Automated process</p>
                          )}
                        </div>
                      </div>

                      {isCurrent && (
                        <div className="flex items-center space-x-2">
                          {level.id === 2 && (
                            <div className="min-w-48">
                             
                            </div>
                          )}
                          {level.id < 4 && (
                            <Button
                              variant="primary"
                              size="sm"
                              onClick={() => handleLevelProgression(level.id + 1)}
                              disabled={levelUpdateLoading || (level.id === 2 && !selectedInterviewer)}
                              loading={levelUpdateLoading}
                              className="whitespace-nowrap"
                            >
                              <Icon name="arrow-right" size="sm" className="mr-1" />
                              Progress to Level {level.id + 1}
                            </Button>
                          )}
                          {level.id === 4 && (
                            <Badge variant="success" size="lg">
                              Final Level
                            </Badge>
                          )}
                        </div>
                      )}
                    </div>
                  </div>
                );
              })}
            </div>
          </Card>

          {/* Experience Summary */}
          <Card title="Experience Summary" padding="lg">
            <div className="prose max-w-none">
              <p className="text-gray-700 whitespace-pre-wrap">{candidate.experience_summary}</p>
            </div>
          </Card>

          {/* Employment History */}
          <Card title="Employment History" padding="lg">
            <div className="space-y-4">
              {candidate.employment_history.map((job, index) => (
                <div key={index} className="border-l-4 border-blue-500 pl-4">
                  <h4 className="font-medium text-gray-900">{job.role}</h4>
                  <p className="text-gray-600">{job.organization}</p>
                  <p className="text-sm text-gray-500">
                    {job.start_date} - {job.end_date}
                  </p>
                </div>
              ))}
            </div>
          </Card>

          {/* Education */}
          <Card title="Education" padding="lg">
            <div className="space-y-4">
              {candidate.education.map((edu, index) => (
                <div key={index} className="border-l-4 border-green-500 pl-4">
                  <h4 className="font-medium text-gray-900">{edu.degree}</h4>
                  <p className="text-gray-600">{edu.field_of_study}</p>
                  {edu.institution && (
                    <p className="text-gray-600">{edu.institution}</p>
                  )}
                  <p className="text-sm text-gray-500">
                    {edu.start_date} - {edu.end_date}
                  </p>
                </div>
              ))}
            </div>
          </Card>

          {/* Interview History */}
          {candidate.interview_eval_details && candidate.interview_eval_details.length > 0 && (
            <Card title="Interview History & Evaluations" padding="lg">
              <div className="space-y-4">
                {candidate.interview_eval_details.map((interview: any, index: number) => {
                  const levelInfo = INTERVIEW_LEVELS.find(l => l.id === parseInt(interview.level.replace('level', ''))) || INTERVIEW_LEVELS[index];
                  const isPass = interview.decision === 'pass' && interview.status === 'Completed';
                  const isFail = interview.decision === 'fail' || interview.decision === 'rejected';

                  return (
                    <div key={index} className={`border-2 rounded-lg p-4 ${
                      isPass ? 'border-green-200 bg-green-50' :
                      isFail ? 'border-red-200 bg-red-50' :
                      'border-yellow-200 bg-yellow-50'
                    }`}>
                      <div className="flex items-start justify-between mb-3">
                        <div className="flex items-center space-x-3">
                          <div className={`w-8 h-8 rounded-full flex items-center justify-center text-white font-bold ${
                            isPass ? 'bg-green-500' :
                            isFail ? 'bg-red-500' :
                            'bg-yellow-500'
                          }`}>
                            {interview.level || index + 1}
                          </div>
                          <div>
                            <h4 className="font-medium text-gray-900">
                              {levelInfo?.name || `Level ${interview.level || index + 1} Interview`}
                            </h4>
                            <p className="text-sm text-gray-600">{levelInfo?.description}</p>
                          </div>
                        </div>
                        <Badge variant={isPass ? 'success' : isFail ? 'danger' : 'warning'}>
                          {isPass ? 'Passed' : isFail ? 'Failed' : interview.status || 'Pending'}
                        </Badge>
                      </div>

                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-3">
                        {interview.interviewer && (
                          <div>
                            <label className="block text-xs font-medium text-gray-700 mb-1">Interviewer</label>
                            <p className="text-sm text-gray-900">{interview.interviewer}</p>
                          </div>
                        )}
                        {interview.start_time && (
                          <div>
                            <label className="block text-xs font-medium text-gray-700 mb-1">Date & Time</label>
                            <p className="text-sm text-gray-900">
                              {new Date(interview.start_time).toLocaleString()}
                            </p>
                          </div>
                        )}
                        {interview.duration && (
                          <div>
                            <label className="block text-xs font-medium text-gray-700 mb-1">Duration</label>
                            <p className="text-sm text-gray-900">{interview.duration}</p>
                          </div>
                        )}
                        {interview.score && (
                          <div>
                            <label className="block text-xs font-medium text-gray-700 mb-1">Score</label>
                            <p className="text-sm text-gray-900">{interview.score}</p>
                          </div>
                        )}
                      </div>

                      {interview.summary && (
                        <div className="mb-3">
                          <label className="block text-xs font-medium text-gray-700 mb-1">Summary</label>
                          <p className="text-sm text-gray-700 bg-white p-2 rounded border">
                            {interview.summary}
                          </p>
                        </div>
                      )}

                      {interview.interviewnotes && (
                        <div>
                          <label className="block text-xs font-medium text-gray-700 mb-1">Interview Notes</label>
                          <p className="text-sm text-gray-700 bg-white p-2 rounded border whitespace-pre-wrap">
                            {interview.interviewnotes}
                          </p>
                        </div>
                      )}

                      {interview.feedback && (
                        <div className="mt-3">
                          <label className="block text-xs font-medium text-gray-700 mb-1">Feedback</label>
                          <p className="text-sm text-gray-700 bg-white p-2 rounded border">
                            {interview.feedback}
                          </p>
                        </div>
                      )}
                    </div>
                  );
                })}
              </div>

              {/* Summary Statistics */}
              <div className="mt-6 pt-4 border-t border-gray-200">
                <h5 className="font-medium text-gray-900 mb-3">Interview Summary</h5>
                <div className="grid grid-cols-3 gap-4 text-center">
                  <div className="bg-green-50 p-3 rounded-lg">
                    <div className="text-lg font-bold text-green-600">
                      {candidate.interview_eval_details.filter((i: any) =>
                        i.decision === 'pass' || i.decision === 'Scheduled'
                      ).length}
                    </div>
                    <div className="text-xs text-green-700">Passed</div>
                  </div>
                  <div className="bg-red-50 p-3 rounded-lg">
                    <div className="text-lg font-bold text-red-600">
                      {candidate.interview_eval_details.filter((i: any) =>
                        i.decision === 'fail' || i.decision === 'rejected'
                      ).length}
                    </div>
                    <div className="text-xs text-red-700">Failed</div>
                  </div>
                  <div className="bg-yellow-50 p-3 rounded-lg">
                    <div className="text-lg font-bold text-yellow-600">
                     {candidate.interview_eval_details.filter((i: any) =>
  (!i.decision || i.decision === 'pass') && i.status === 'Scheduled'
).length}

                    </div>
                    <div className="text-xs text-yellow-700">Pending</div>
                  </div>
                </div>
              </div>
            </Card>
          )}
        </div>

        {/* Right Column - Sidebar */}
        <div className="space-y-6">
          {/* Resume Preview */}
          <Card title="Resume" padding="lg">
            <div className="space-y-4">
              <Button
                variant="outline"
                className="w-full"
                onClick={() => setShowResumePreview(!showResumePreview)}
              >
                <Icon name="file" size="sm" className="mr-2" />
                {showResumePreview ? 'Hide Preview' : 'Show Preview'}
              </Button>
              <Button
                variant="primary"
                className="w-full"
                onClick={() => window.open(`/api/download-resume?id=${candidate.fileid}`, '_blank')}
              >
                <Icon name="download" size="sm" className="mr-2" />
                Download Resume
              </Button>

              {showResumePreview && (
                <div className="mt-4">
                  <div className="border rounded-lg overflow-hidden bg-gray-50">
                    <iframe
                      src={`/api/download-resume?id=${candidate.fileid}`}
                      className="w-full h-96"
                      title="Resume Preview"
                      onError={() => {
                        console.error('Failed to load resume preview');
                      }}
                    />
                  </div>
                  <p className="text-xs text-gray-500 mt-2 text-center">
                    If the preview doesn't load, click "Download Resume" to view the file.
                  </p>
                </div>
              )}
            </div>
          </Card>

          {/* Candidate Info */}
          <Card title="Candidate Information" padding="lg">
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Experience
                </label>
                <p className="text-gray-900">{candidate.years_of_experience} years</p>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Location
                </label>
                <p className="text-gray-900">{candidate.current_location}</p>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Relocation
                </label>
                <p className="text-gray-900">{candidate.flexible_with_relocation}</p>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Notice Period
                </label>
                <p className="text-gray-900">{candidate.current_notice_period}</p>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Current CTC
                </label>
                <p className="text-gray-900">{candidate.current_ctc}</p>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Expected CTC
                </label>
                <p className="text-gray-900">{candidate.expected_ctc}</p>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Applied Date
                </label>
                <p className="text-gray-900">{formatDateConsistent(new Date(candidate.created_on))}</p>
              </div>
            </div>
          </Card>

          {/* Skills */}
          <Card title="Skills" padding="lg">
            <div className="flex flex-wrap gap-2">
              {candidate.key_skills.map((skill, index) => (
                <Badge key={index} variant="default" size="sm">
                  {skill}
                </Badge>
              ))}
            </div>
          </Card>

          {/* Achievements */}
          {candidate.achievements.length > 0 && (
            <Card title="Achievements" padding="lg">
              <ul className="space-y-2">
                {candidate.achievements.map((achievement, index) => (
                  <li key={index} className="text-sm text-gray-700">
                    • {achievement}
                  </li>
                ))}
              </ul>
            </Card>
          )}

          {/* Certifications */}
          {candidate.certifications.length > 0 && (
            <Card title="Certifications" padding="lg">
              <ul className="space-y-2">
                {candidate.certifications.map((cert, index) => (
                  <li key={index} className="text-sm text-gray-700">
                    • {cert}
                  </li>
                ))}
              </ul>
            </Card>
          )}

          {/* Actions */}
          <Card title="Actions" padding="lg">
            <div className="space-y-3">
              <Button variant="primary" className="w-full">
                <Icon name="mail" size="sm" className="mr-2" />
                Contact Candidate
              </Button>
              <Button variant="outline" className="w-full">
                <Icon name="calendar" size="sm" className="mr-2" />
                Schedule Interview
              </Button>
              <Button variant="outline" className="w-full" onClick={() => router.push('/candidates')}>
                <Icon name="list" size="sm" className="mr-2" />
                View All Candidates
              </Button>
            </div>
          </Card>
        </div>
      </div>
    </div>
  );
}
