import React from 'react';
import { cn } from '@/lib/utils';
import { Input, Icon } from '@/components/ui';

interface FormFieldProps {
  label: string;
  name: string;
  type?: 'text' | 'email' | 'password' | 'number' | 'tel' | 'url' | 'textarea' | 'select' | 'date';
  value: string;
  onChange: (name: string, value: string) => void;
  placeholder?: string;
  required?: boolean;
  disabled?: boolean;
  error?: string;
  helpText?: string;
  options?: { value: string; label: string }[];
  rows?: number;
  className?: string;
   min?: number | string;
  max?: number | string;
  step?: number | string;
}

const FormField: React.FC<FormFieldProps> = ({
  label,
  name,
  type = 'text',
  value,
  onChange,
  placeholder,
  required = false,
  disabled = false,
  error,
  helpText,
  options = [],
  rows = 3,
  className,
  min,
  max,
  step,
}) => {
  const handleChange = (newValue: string) => {
    onChange(name, newValue);
  };

 const renderInput = () => {
  const baseClasses = 'block w-full rounded-md border-black-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm transition duration-200 ease-in-out';
  const errorClasses = 'border-red-300 text-red-900 placeholder-red-300 focus:border-red-500 focus:ring-red-500';
  const disabledClasses = 'bg-gray-100 text-gray-500 cursor-not-allowed opacity-75';
  const textareaClasses = 'min-h-[100px]'; // Minimum height for textareas
  const inputHeightClasses = 'py-2 px-3 h-10'; // Standard input height
  const selectClasses = 'pr-8 text-black'; // Extra padding for select dropdown arrow

  const inputClasses = cn(
    baseClasses,
    error && errorClasses,
    disabled && disabledClasses,
    type === 'textarea' ? textareaClasses : inputHeightClasses,
    type === 'select' && selectClasses
  );

  switch (type) {
    case 'textarea':
      return (
        <textarea
          id={name}
          name={name}
          value={value}
          onChange={(e) => handleChange(e.target.value)}
          placeholder={placeholder}
          disabled={disabled}
          rows={rows || 4}
          className={cn(inputClasses, 'resize-y min-h-[100px]')}
          style={{ minHeight: '100px' }}
        />
      );

    case 'select':
      return (
        <div className="relative">
          <select
            id={name}
            name={name}
            value={value}
            onChange={(e) => handleChange(e.target.value)}
            disabled={disabled}
            className={inputClasses}
          >
            {placeholder && (
              <option value="" disabled>
                {placeholder}
              </option>
            )}
            {options?.map((option) => (
              <option key={option.value} value={option.value}>
                {option.label}
              </option>
            ))}
          </select>
          {!disabled && (
            <div className="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-2">
              <svg className="h-5 w-5 text-gray-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clipRule="evenodd" />
              </svg>
            </div>
          )}
        </div>
      );

    default:
      return (
        <input
          type={type}
          id={name}
          name={name}
          value={value}
          onChange={(e) => handleChange(e.target.value)}
          placeholder={placeholder}
          disabled={disabled}
          className={inputClasses}
          min={type === 'number' ? min : undefined}
          max={type === 'number' ? max : undefined}
          step={type === 'number' ? step : undefined}
        />
      );
  }
};

  return (
    <div className={cn('space-y-1', className)}>
      <label htmlFor={name} className="block text-sm font-medium text-black-700">
        {label}
        {required && <span className="text-red-500 ml-1">*</span>}
      </label>
      
      {renderInput()}
      
      {error && (
        <div className="flex items-center mt-1">
          <Icon name="alert" size="sm" className="text-red-500 mr-1" />
          <p className="text-sm text-red-600">{error}</p>
        </div>
      )}
      
      {helpText && !error && (
        <p className="text-sm text-black-500">{helpText}</p>
      )}
    </div>
  );
};

export default FormField;
