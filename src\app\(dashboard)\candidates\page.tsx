'use client';

import React, { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import {
  <PERSON>ton,
  Card,
  SearchBar,
  FilterDropdown,
  DataTable,
  Badge,
  CandidateCard
} from '@/components';
import { Candidate } from '@/types';
import { cn, formatRelativeTime } from '@/lib/utils';

export default function CandidatesPage() {
  const router = useRouter();
  const [searchQuery, setSearchQuery] = React.useState('');
  const [statusFilters, setStatusFilters] = React.useState<string[]>([]);
  const [skillFilters, setSkillFilters] = React.useState<string[]>([]);
   const [isLoading, setIsLoading] = React.useState(false);
  const [recentCandidateData, setRecentCandidateData] = React.useState<any>([]);
  const [allCandidateData, setAllCandidateData] = React.useState<any>([]);


     const fetchCandidate = async () => {
  try {
    setIsLoading(true);
    const response = await fetch('/api/candidate');

    if (!response.ok) {
      throw new Error('Failed to fetch candidate');
    }

    const data = await response.json();

    console.log(data?.Items, "<<<<<<< Candidate Data >>>>>>");
    setAllCandidateData(data?.Items);

    // Sort and get latest 4 by created_on
    const sortedData = data?.Items?.filter(item => item.created_on).sort((a, b) => new Date(b.created_on).getTime() - new Date(a.created_on).getTime());

    const latestFour = sortedData.slice(0, 4);

    console.log(latestFour, "<<<<<<< Recent 4 Candidates >>>>>>");
    setRecentCandidateData(latestFour);
  } catch (error) {
    console.error('Error fetching candidate:', error);
  } finally {
    setIsLoading(false);
  }
};

useEffect(() => {
  fetchCandidate();
}, []);

console.log(recentCandidateData, "<<<<<<< Recent Candidate Data >>>>>>");


  // Sample candidate data
  const candidates: Candidate[] = [
    {
      id: '1',
      name: 'Sarah Johnson',
      email: '<EMAIL>',
      phone: '+****************',
      status: 'screening',
      appliedJobs: ['1'],
      skills: ['React', 'TypeScript', 'Node.js', 'Python'],
      experience: 5,
      location: 'San Francisco, CA',
      appliedDate: new Date('2024-01-20')
    },
    {
      id: '2',
      name: 'Mike Chen',
      email: '<EMAIL>',
      phone: '+****************',
      status: 'interview',
      appliedJobs: ['1', '3'],
      skills: ['JavaScript', 'React', 'AWS', 'Docker'],
      experience: 7,
      location: 'Seattle, WA',
      appliedDate: new Date('2024-01-18')
    },
    {
      id: '3',
      name: 'Emily Rodriguez',
      email: '<EMAIL>',
      phone: '+****************',
      status: 'offer',
      appliedJobs: ['2'],
      skills: ['Figma', 'Adobe XD', 'User Research', 'Prototyping'],
      experience: 4,
      location: 'Austin, TX',
      appliedDate: new Date('2024-01-15')
    },
    {
      id: '4',
      name: 'David Kim',
      email: '<EMAIL>',
      status: 'hired',
      appliedJobs: ['3'],
      skills: ['Python', 'Django', 'PostgreSQL', 'Redis'],
      experience: 6,
      location: 'New York, NY',
      appliedDate: new Date('2024-01-10')
    }
  ];

  const statusOptions = [
    { value: 'new', label: 'New', count: 0 },
    { value: 'screening', label: 'Screening', count: 1 },
    { value: 'interview', label: 'Interview', count: 1 },
    { value: 'offer', label: 'Offer', count: 1 },
    { value: 'hired', label: 'Hired', count: 1 },
    { value: 'rejected', label: 'Rejected', count: 0 }
  ];

  const skillOptions = [
    { value: 'react', label: 'React', count: 2 },
    { value: 'typescript', label: 'TypeScript', count: 1 },
    { value: 'python', label: 'Python', count: 2 },
    { value: 'figma', label: 'Figma', count: 1 },
    { value: 'aws', label: 'AWS', count: 1 }
  ];

  const tableColumns = [
    { key: 'name', title: 'Name', sortable: true },
    { key: 'email', title: 'Email', sortable: true },
    { key: 'experience', title: 'Experience', sortable: true },
    { 
      key: 'status', 
      title: 'Status', 
      render: (value: string) => (
        <Badge variant={
          value === 'hired' ? 'success' : 
          value === 'offer' ? 'success' : 
          value === 'interview' ? 'warning' : 
          'default'
        }>
          {value}
        </Badge>
      )
    },
    { key: 'appliedDate', title: 'Applied Date', sortable: true }
  ];

  const tableData = allCandidateData?.map(candidate => ({
    ...candidate,
    experience: `${candidate?.years_of_experience} years`,
    appliedDate: candidate.created_on
  }));

  const handleCandidateAction = (action: string, candidate: any) => {
    if (action === 'view') {
      router.push(`/candidates/${candidate.candidateid}`);
    } else {
      console.log(`${action} candidate:`, candidate);
    }
  };

  const handleBulkUpload = () => {
    console.log('Bulk upload resumes');
  };

  return (
    <div className="space-y-6">
      {/* Header Actions */}
      <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-4">
        <div>
          <h1 className="text-xl sm:text-2xl font-bold text-gray-900">Candidates</h1>
          <p className="text-gray-600">Manage and review candidate applications</p>
        </div>
        <div className="flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-3">
          <Button variant="outline" onClick={handleBulkUpload} className="w-full sm:w-auto">
            Bulk Upload
          </Button>
          <Button variant="primary" className="w-full sm:w-auto">
            Add Candidate
          </Button>
        </div>
      </div>

      {/* Search and Filters */}
      <Card padding="md">
        <div className="space-y-4">
          <SearchBar
            placeholder="Search candidates by name, email, or skills..."
            value={searchQuery}
            onChange={setSearchQuery}
            onSearch={(query) => console.log('Search:', query)}
          />
          
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
            <FilterDropdown
              title="Status"
              options={statusOptions}
              selectedValues={statusFilters}
              onChange={setStatusFilters}
              placeholder="Filter by status"
            />
            <FilterDropdown
              title="Skills"
              options={skillOptions}
              selectedValues={skillFilters}
              onChange={setSkillFilters}
              placeholder="Filter by skills"
            />
          </div>
        </div>
      </Card>

    {isLoading ? (
  <div className="space-y-6">
    <Card padding="md">
      <div className="p-8 text-center">
        <div className="animate-spin inline-block w-6 h-6 border-2 border-blue-600 border-t-transparent rounded-full"></div>
        <p className="mt-2 text-sm text-gray-600">Loading...</p>
      </div>
    </Card>
  </div>
) : (
  <>
    <div>
      <h2 className="text-lg font-semibold text-gray-900 mb-4">Recent Applications</h2>
      <div className="space-y-4">
        {recentCandidateData?.map((candidate) => (
          <CandidateCard
            key={candidate?.candidateid}
            candidate={candidate}
            onView={() => handleCandidateAction('view', candidate)}
            onContact={() => handleCandidateAction('contact', candidate)}
            onScheduleInterview={() => handleCandidateAction('schedule', candidate)}
          />
        ))}
      </div>
    </div>

    <div className="mt-8">
      <h2 className="text-lg font-semibold text-gray-900 mb-4">All Candidates</h2>
      <DataTable
        loading={isLoading}
        columns={tableColumns}
        data={tableData || []}
        onSort={(key, direction) => console.log('Sort:', key, direction)}
        onRowClick={(row) => router.push(`/candidates/${row.candidateid}`)}
      />
    </div>
  </>
)}

     
    </div>
  );
}
