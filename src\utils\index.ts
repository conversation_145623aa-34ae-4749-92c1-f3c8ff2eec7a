import moment from "moment";
import { NextRequest } from "next/server";

export const isAdmin = async () => {
  return !true;
};

export const searchParams = (request: NextRequest, param: string) =>
  new URL(request.url).searchParams.get(param)?.toLowerCase();

/**
 * Returns true if the given value is a non-empty string after stripping
 * any HTML tags, or a string error message if the value is empty.
 *
 * @param {string} value The value to validate.
 * @param {string} label A label to include in the error message.
 * @returns {boolean|string} true, or an error message.
 */
export const textOnlyValidation = (
  value: string,
  label: string,
): boolean | string => {
  const strippedValue = value.replace(/<[^>]*>/g, "").trim();
  return strippedValue.length > 0 || `${label} cannot be empty`;
};

export const expenseLabelRender = (expenseType: string) => {
  switch (expenseType) {
    case "food":
      return "Food"; // example for 'food' case
    case "accommodation":
      return "Accommodation"; // example for 'accommodation' case
    case "local_commute":
      return "Local Commute"; // example for 'commute' case
    case "other":
      return "Other";

    default:
      return "N/A"; // default case if no match
  }
};

// export const formatDate = (dateStr: string) => {
//   const parsedDate = new Date(dateStr);
//   if (isNaN(parsedDate.getTime())) return "Invalid date";

//   const year = parsedDate.getFullYear();
//   const month = String(parsedDate.getMonth() + 1).padStart(2, "0"); // GetMonth is 0-indexed
//   const day = String(parsedDate.getDate()).padStart(2, "0");

//   return `${year}-${month}-${day}`;
// };

export const formatDate = (dateString: string) => {
  const formats = [
    "YYYY-MM-DD",
    "DD-MM-YYYY",
    "D/M/YYYY",
    "MMMM D, YYYY",
    "D MMMM YYYY",
  ];
  return moment(dateString, formats, true).isValid()
    ? moment(dateString, formats).format("YYYY-MM-DD")
    : "Invalid date"; // Handle invalid date cases
};

// type APIMethods = "GET" | "POST" | "PUT" | "DELETE" | "PATCH";

/**
 * Checks if the given API URL, roles and method has access based on the
 * data in `api_access.json`.
 *
 * @param {string} apiUrl The API URL to check.
 * @param {string[]} roles The roles of the user to check.
 * @param {string} method The method of the API request to check.
 * @returns {boolean} true if the API URL, roles and method have access, false otherwise.
 */
export function getRoleBaseAccess(
  apiUrl: string,
  roles: string[],
  method: string,
): boolean {
  // Import the JSON data
  const apiAccess = require("./api_access.json");

  // Iterate over all keys in the JSON object
  for (const key in apiAccess) {
    if (apiAccess.hasOwnProperty(key)) {
      // Find the API entry that matches the given URL
      const apiEntry = apiAccess[key].find(
        (entry: any) => entry.path === apiUrl,
      );

      if (apiEntry) {
        // Check if the method is allowed
        const methodAllowed =
          apiEntry.method.includes("*") ||
          apiEntry.method
            .map((m: string) => m.toUpperCase())
            .includes(method.toUpperCase());

        if (!methodAllowed) {
          // If the method is not allowed, continue to the next entry
          continue;
        }

        // Check if any of the roles are allowed
        const roleAllowed = roles.some((role) => apiEntry.roles.includes(role));

        if (roleAllowed) {
          return true;
        }
      }
    }
  }

  // If no matching API entry is found, return false
  return false;
}

/**
 * Checks if the given action and roles have access based on the
 * data in `ui_access.json`.
 *
 * @param {string} action The action to check.
 * @param {string[]} roles The roles of the user to check.
 * @returns {boolean} true if the action and roles have access, false otherwise.
 */
export function getUIRoleBaseAccess(
  action: string,
  roles: string[] = [],
): boolean {
  // Import the JSON data
  const uiAccess = require("./ui_access.json");

  // Iterate over all keys in the JSON object
  for (const key in uiAccess) {
    if (uiAccess.hasOwnProperty(key)) {
      // Find the UI entry that matches the given action
      const uiEntry = uiAccess[key].find(
        (entry: any) => entry.action === action,
      );

      if (uiEntry) {
        // Check if any of the roles are allowed
        const roleAllowed = roles.some((role) => uiEntry.roles.includes(role));

        if (roleAllowed) {
          return true;
        }
      }
    }
  }

  // If no matching UI entry is found, return false
  return false;
}

// * candidate status list format { value: "test option", label: "Test Option" }
export const candidateStatusList = [
  { value: "resume extracting", label: "Resume Extracting" },
  { value: "initial", label: "Initial" },
  { value: "ai screening scheduled", label: "AI Screening Scheduled" },
  { value: "passed ai screening", label: "Passed AI Screening" },
  { value: "coding test scheduled", label: "Coding Test Scheduled" },
  { value: "passed coding test", label: "Passed Coding Test" },
  { value: "l1 scheduled", label: "L1 Scheduled" },
  { value: "passed l1", label: "Passed L1" },
  { value: "l2 scheduled", label: "L2 Scheduled" },
  { value: "passed l2", label: "Passed L2" },
  { value: "l3 scheduled", label: "L3 Scheduled" },
  { value: "selected", label: "Selected" },
  { value: "offer rolledout", label: "Offer Rolled Out" },
  { value: "candidate confirmed", label: "Candidate Confirmed" },
  { value: "candidate joined", label: "Candidate Joined" },
  { value: "rejected", label: "Rejected" },
  { value: "candidate not responding", label: "Candidate Not Responding" },
  { value: "candidate aborted", label: "Candidate Aborted" },
  { value: "on hold", label: "On Hold" },
];
function parseJSONResponse(response: string) {
  try {
    // Trim whitespace
    response = response.trim();

    // Check if response starts with a markdown-style JSON block
    if (response.startsWith("```json")) {
      response = response.replace(/^```json|```$/g, "").trim();
    }

    // Parse JSON string
    return JSON.parse(response);
  } catch (error) {
    console.error("Invalid JSON format:", error);
    return null; // Return null or handle error appropriately
  }
}

// Example usage with argument
export function logParsedJSONResponse(responseString: string) {
  const parsedData = parseJSONResponse(responseString);
  if (parsedData) {
    return parsedData;
  } else {
    console.log("Failed to parse JSON");
  }
}
