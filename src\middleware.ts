import {
  withMiddlewareAuthRequired,
  getSession,
} from "@auth0/nextjs-auth0/edge";
import { NextFetchEvent, type NextRequest, NextResponse } from "next/server";
import { getRoleBaseAccess } from "./utils";

//* Routes that don't require authentication
const publicRoutes = [
  "/",
  "/api/auth/login",
  "/api/auth/logout",
  "/api/auth/callback",
  "/api/waitlist",
];

export default async function middleware(
  req: NextRequest,
  event: NextFetchEvent,
) {
  const { pathname } = req.nextUrl;
  const res = NextResponse.next();
  const user = await getSession(req, res);
  // Allow access to public routes without authentication
  if (publicRoutes.includes(pathname)) {
    if (user && req.nextUrl.pathname === "/") {
      return NextResponse.redirect(new URL("/dashboard", req.url));
    }
    return NextResponse.next();
  }

  // For all other routes, apply authentication
  return withMiddlewareAuthRequired(async function (req: NextRequest) {
    let ROLE = [];
    //* Get employee data
    try {
      const ress = await fetch(
        `${process.env.NODE_RED_URL}/Get-Employee-By-Id1?email=${user?.user?.email}`,
        {
          cache: "force-cache",
          headers: {
            context_user: user?.user?.email as string,
          },
        },
      );

      if (!ress.ok) {
        throw new Error("Failed to fetch employee data");
      }

      const data = await ress.json();
      ROLE = data?.Items?.[0]?.role;

      res.headers.set("X-Auth-User-Role", ROLE);
      res.headers.set("X-Auth-User-ID", user?.user?.sub as string);
      res.headers.set("X-Auth-User-Email", user?.user?.email as string);
      res.headers.set("X-Auth-User-Name", data?.Items?.[0]?.name as string);
      res.headers.set(
        "X-Auth-User-Organization",
        data?.Items?.[0]?.organization as string,
      );

      if (req.nextUrl.pathname.startsWith("/api/expense")) {
        if (!getRoleBaseAccess(req.nextUrl.pathname, ROLE, req.method)) {
          return NextResponse.json(
            { message: "Access Denied", ROLE },
            { status: 403 },
          );
        }
      }
    } catch (err) {
      console.error(err);
      return NextResponse.redirect(new URL("/error", req.url));
    }

    return res;
  })(req, event);
}

export const config = {
  matcher: ["/((?!_next/static|_next/image|favicon.ico|robots.txt).*)"],
};
