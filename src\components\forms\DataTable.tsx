import React, { useState } from 'react';
import { cn } from '@/lib/utils';
import { Icon, Button, Badge, Dropdown } from '@/components/ui';

interface Column {
  key: string;
  title: string;
  sortable?: boolean;
  render?: (value: any, row: any) => React.ReactNode;
  width?: string;
}

interface DataTableProps {
  columns: Column[];
  data: any[];
  loading?: boolean;
  sortBy?: string;
  sortDirection?: 'asc' | 'desc';
  onSort?: (key: string, direction: 'asc' | 'desc') => void;
  onRowClick?: (row: any) => void;
  emptyMessage?: string;
  className?: string;
}

const DataTable: React.FC<DataTableProps> = ({
  columns,
  data,
  loading,
  sortBy,
  sortDirection = 'asc',
  onSort,
  onRowClick,
  emptyMessage = 'No data available',
  className,
}) => {
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(10);

  // Available options for items per page
  const itemsPerPageOptions = [10, 25, 50, 100];
  const handleSort = (columnKey: string) => {
    if (!onSort) return;
    
    const newDirection = 
      sortBy === columnKey && sortDirection === 'asc' ? 'desc' : 'asc';
    onSort(columnKey, newDirection);
  };

  const renderSortIcon = (columnKey: string) => {
    if (sortBy !== columnKey) {
      return <Icon name="chevronDown" size="sm" className="text-gray-300" />;
    }
    
    return (
      <Icon 
        name="chevronDown" 
        size="sm" 
        className={cn(
          'text-blue-600',
          sortDirection === 'asc' && 'transform rotate-180'
        )} 
      />
    );
  };

  const renderCellContent = (column: Column, row: any) => {
    const value = row[column.key];
    
    if (column.render) {
      return column.render(value, row);
    }
    
    return value;
  };

  // Paginate data
  const totalPages = Math.ceil(data.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const paginatedData = data.slice(startIndex, startIndex + itemsPerPage);

  // Navigation functions
  const handleFirst = () => setCurrentPage(1);
  const handlePrev = () => setCurrentPage((prev) => Math.max(prev - 1, 1));
  const handleNext = () => setCurrentPage((prev) => Math.min(prev + 1, totalPages));
  const handleLast = () => setCurrentPage(totalPages);

  // Handle page selection from dropdown
  const handlePageSelect = (page: number) => setCurrentPage(page);

  // Handle items per page change
  const handleItemsPerPageChange = (count: number) => {
    setItemsPerPage(count);
    // Reset to first page when changing items per page
    setCurrentPage(1);
  };

  // Calculate display range for results summary
  const startItem = data.length === 0 ? 0 : (currentPage - 1) * itemsPerPage + 1;
  const endItem = Math.min(currentPage * itemsPerPage, data.length);

  if (loading) {
    return (
      <div className={cn('bg-white rounded-lg border border-gray-200', className)}>
        <div className="p-8 text-center">
          <div className="animate-spin inline-block w-6 h-6 border-2 border-blue-600 border-t-transparent rounded-full"></div>
          <p className="mt-2 text-sm text-gray-600">Loading...</p>
        </div>
      </div>
    );
  }

  return (
     <div className={cn('bg-white rounded-lg border border-gray-200 overflow-hidden', className)}>
      <div className="overflow-x-auto">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              {columns.map((column) => (
                <th
                  key={column.key}
                  className={cn(
                    'px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider',
                    column.sortable && 'cursor-pointer hover:bg-gray-100',
                    column.width && `w-${column.width}`
                  )}
                  onClick={() => column.sortable && handleSort(column.key)}
                >
                  <div className="flex items-center space-x-1">
                    <span>{column.title}</span>
                    {column.sortable && renderSortIcon(column.key)}
                  </div>
                </th>
              ))}
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {paginatedData.length === 0 ? (
              <tr>
                <td colSpan={columns.length} className="px-6 py-8 text-center text-sm text-gray-500">
                  {emptyMessage}
                </td>
              </tr>
            ) : (
              paginatedData.map((row, index) => (
                <tr
                  key={index}
                  className={cn('hover:bg-gray-50', onRowClick && 'cursor-pointer')}
                  onClick={() => onRowClick?.(row)}
                >
                  {columns.map((column) => (
                    <td
                      key={column.key}
                      className="px-6 py-4 whitespace-nowrap text-sm text-gray-900"
                    >
                      {renderCellContent(column, row)}
                    </td>
                  ))}
                </tr>
              ))
            )}
          </tbody>
        </table>
      </div>

      {/* Enhanced Pagination Controls */}
      {data.length > 0 && (totalPages > 1 || data.length > Math.min(...itemsPerPageOptions)) && (
        <div className="border-t bg-gray-50">
          {/* Mobile Pagination */}
          <div className="flex items-center justify-between px-4 py-3 sm:hidden">
            <div className="flex items-center space-x-2">
              <Button
                onClick={handleFirst}
                disabled={currentPage === 1}
                size="sm"
                variant="outline"
              >
                <Icon name="chevrons-left" size="sm" />
              </Button>
              <Button
                onClick={handlePrev}
                disabled={currentPage === 1}
                size="sm"
                variant="outline"
              >
                <Icon name="chevronLeft" size="sm" />
              </Button>
            </div>

            <span className="text-sm text-gray-600">
              {startItem}-{endItem} of {data.length}
            </span>

            <div className="flex items-center space-x-2">
              <Button
                onClick={handleNext}
                disabled={currentPage === totalPages}
                size="sm"
                variant="outline"
              >
                <Icon name="chevronRight" size="sm" />
              </Button>
              <Button
                onClick={handleLast}
                disabled={currentPage === totalPages}
                size="sm"
                variant="outline"
              >
                <Icon name="chevrons-right" size="sm" />
              </Button>
            </div>
          </div>

          {/* Desktop Pagination */}
          <div className="hidden sm:flex items-center justify-between px-4 py-3">
            {/* Left side - Results summary and items per page */}
            <div className="flex items-center space-x-4">
              <span className="text-sm text-gray-600">
                Showing {startItem}-{endItem} of {data.length} results
              </span>

              <div className="flex items-center space-x-2">
                <span className="text-sm text-gray-600">Show:</span>
                <Dropdown
                  trigger={
                    <Button variant="outline" size="sm">
                      {itemsPerPage}
                      <Icon name="chevron-down" size="sm" className="ml-1" />
                    </Button>
                  }
                  items={itemsPerPageOptions.map(count => ({
                    label: count.toString(),
                    onClick: () => handleItemsPerPageChange(count)
                  }))}
                />
                <span className="text-sm text-gray-600">per page</span>
              </div>
            </div>

            {/* Right side - Navigation controls */}
            <div className="flex items-center space-x-2">
              <Button
                onClick={handleFirst}
                disabled={currentPage === 1}
                size="sm"
                variant="outline"
              >
                <Icon name="chevrons-left" size="sm" />
              </Button>
              <Button
                onClick={handlePrev}
                disabled={currentPage === 1}
                size="sm"
                variant="outline"
              >
                <Icon name="chevronLeft" size="sm" />
              </Button>

              {/* Page selection dropdown */}
              <div className="flex items-center space-x-2">
                <span className="text-sm text-gray-600">Page:</span>
                <Dropdown
                  trigger={
                    <Button variant="outline" size="sm">
                      {currentPage}
                      <Icon name="chevron-down" size="sm" className="ml-1" />
                    </Button>
                  }
                  items={Array.from({ length: totalPages }, (_, i) => i + 1).map(page => ({
                    label: page.toString(),
                    onClick: () => handlePageSelect(page)
                  }))}
                />
                <span className="text-sm text-gray-600">of {totalPages}</span>
              </div>

              <Button
                onClick={handleNext}
                disabled={currentPage === totalPages}
                size="sm"
                variant="outline"
              >
                <Icon name="chevronRight" size="sm" />
              </Button>
              <Button
                onClick={handleLast}
                disabled={currentPage === totalPages}
                size="sm"
                variant="outline"
              >
                <Icon name="chevrons-right" size="sm" />
              </Button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default DataTable;
