/**
 * Format time in hours to a human-readable format.
 *
 * @param hours The amount of time in hours.
 * @returns A string representing the time in a human-readable format.
 * @example
 *   formatTimeFromHours(3) // Output: 3hr
 *   formatTimeFromHours(0.5) // Output: 30m
 *   formatTimeFromHours(0.001) // Output: 4s
 */
export function formatTimeFromHours(hours: number): string {
  let totalSeconds = Math.round(hours * 3600); // Convert hours to seconds

  let hrs = Math.floor(totalSeconds / 3600);
  totalSeconds %= 3600;

  let mins = Math.floor(totalSeconds / 60);
  let secs = totalSeconds % 60;

  // Return the time in hours, minutes, and seconds
  let result = [];
  if (hrs > 0) result.push(`${hrs}hr`);
  if (mins > 0) result.push(`${mins}m`);

  return result.join(" ");
}
