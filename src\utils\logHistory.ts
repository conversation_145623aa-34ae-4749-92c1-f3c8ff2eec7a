import { z } from "zod";
import { NextRequest } from "next/server";
import axios from "axios";

const HistorySchema = z.object({
  action: z.string(),
  description: z.string(),
  context_user: z.string().optional(),
  attribute_name: z.string(),
  new_value: z.string(),
  object_type: z.string(),
  old_value: z.string(),
  user_email: z.string(),
});

export type HistoryTableSchema = z.infer<typeof HistorySchema>;

export async function logHistory(
  action: string,
  description: string,
  userEmail: string,
  attributeName: string,
  oldValue: string,
  newValue: string,
  objectType: string,
) {
  const logEntry: HistoryTableSchema = {
    action: action,
    description: description,
    attribute_name: "attributeName",
    new_value: "newValue",
    object_type: "objectType",
    old_value: "oldValue",
    user_email: userEmail || "",
  };

  try {
    // Validate the schema
    HistorySchema.parse(logEntry);

    const baseURL = process.env.NEXT_PUBLIC_BASE_URL || "http://localhost:3000";
    console.log("historjjjy");
    console.log("hkkistorjjjy", attributeName);
    console.log("hkkistorjjjy", newValue);
    console.log("hkkistorjjjy", oldValue);
    console.log("hkkistorjjjy", objectType);

    // Make the POST request to /api/history
    const response = await fetch(`${baseURL}/api/history`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "X-Auth-User-Email": userEmail || "",
      },
      body: JSON.stringify(logEntry),
    });

    if (!response.ok) {
      throw new Error(`Failed to log history: ${response.statusText}`);
    }

    const data = await response.json();
    console.log("History log entry successfully sent:", data);
    return data;
  } catch (error) {
    console.error("Failed to send history log entry:", error);
    throw error;
  }
}
