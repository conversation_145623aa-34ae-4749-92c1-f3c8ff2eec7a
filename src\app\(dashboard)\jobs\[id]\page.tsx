'use client';

import React, { useState, useEffect } from 'react';
import { useRouter, useParams } from 'next/navigation';
import { <PERSON><PERSON>, <PERSON>, Badge, Icon } from '@/components';
import { Job } from '@/types';
import { formatDateConsistent } from '@/lib/utils';
import 'quill/dist/quill.snow.css';

// Interface for job data from API
interface ApiJobData {
  jobdescid: string;
  jobtitle: string;
  responsibilities: string;
  requirement: string;
  skills: string;
  location: string;
  status: string;
  publishToExternalChannels: boolean;
  created_by: string;
  organization?: string;
  createdAt?: string;
  updatedAt?: string;
}

// Helper function to convert API data to Job interface
const convertApiJobToJob = (apiJob: ApiJobData): Job => {
  return {
    id: apiJob.jobdescid,
    title: apiJob.jobtitle,
    department: 'General', // Default since not in API
    location: apiJob.location,
    type: 'full-time', // Default since not in API
    status: apiJob.status.toLowerCase() as 'active' | 'paused' | 'closed' | 'draft',
    description: apiJob.responsibilities,
    requirements: apiJob.requirement.split(',').map(req => req.trim()),
    postedDate: apiJob.createdAt ? new Date(apiJob.createdAt) : new Date(),
    closingDate: undefined, // Not available in API
    salary: undefined, // Not available in API
  };
};

export default function JobDetailsPage() {
  const router = useRouter();
  const params = useParams();
  const jobId = params.id as string;
  
  const [job, setJob] = useState<Job | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Fetch job details
  const fetchJobDetails = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const response = await fetch(`/api/jobDescription?id=${jobId}`, {
        headers: {
          'X-Auth-User-Organization': 'demo-org',
          'X-Auth-User-Email': '<EMAIL>',
        },
      });

      if (!response.ok) {
        throw new Error('Failed to fetch job details');
      }

      const data = await response.json();
      
      // Debug: Log the raw data to see what we're getting
      console.log('Raw job data:', data);
      console.log('Responsibilities HTML:', data.responsibilities);
      console.log('Requirements HTML:', data.requirement);
      
      // Convert API data to Job interface
      const convertedJob = convertApiJobToJob(data);
      setJob(convertedJob);
    } catch (err) {
      console.error('Error fetching job details:', err);
      setError(err instanceof Error ? err.message : 'Failed to fetch job details');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (jobId) {
      fetchJobDetails();
    }
  }, [jobId]);

  const handleEdit = () => {
    router.push(`/jobs/${jobId}/edit`);
  };

  const handleDelete = async () => {
    if (!confirm('Are you sure you want to delete this job posting? This action cannot be undone.')) {
      return;
    }

    try {
      const response = await fetch(`/api/jobDescription?jobdescid=${jobId}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        throw new Error('Failed to delete job');
      }

      alert('Job deleted successfully!');
      router.push('/jobs');
    } catch (err) {
      console.error('Error deleting job:', err);
      alert('Failed to delete job. Please try again.');
    }
  };

  const getStatusVariant = (status: string) => {
    switch (status.toLowerCase()) {
      case 'active': return 'success';
      case 'paused': return 'warning';
      case 'closed': return 'danger';
      case 'draft': return 'default';
      default: return 'default';
    }
  };

  // Loading state
  if (loading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center space-x-4">
          <Button variant="ghost" onClick={() => router.back()}>
            <Icon name="arrow-left" size="sm" className="mr-2" />
            Back
          </Button>
        </div>
        
        <div className="flex items-center justify-center py-12">
          <div className="flex items-center space-x-2">
            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
            <span className="text-gray-600">Loading job details...</span>
          </div>
        </div>
      </div>
    );
  }

  // Error state
  if (error || !job) {
    return (
      <div className="space-y-6">
        <div className="flex items-center space-x-4">
          <Button variant="ghost" onClick={() => router.back()}>
            <Icon name="arrow-left" size="sm" className="mr-2" />
            Back
          </Button>
        </div>
        
        <Card padding="lg">
          <div className="text-center py-8">
            <Icon name="alert" size="xl" className="text-red-500 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">Job Not Found</h3>
            <p className="text-gray-600 mb-4">
              {error || 'The job you are looking for does not exist or has been removed.'}
            </p>
            <div className="space-x-4">
              <Button variant="outline" onClick={() => router.back()}>
                Go Back
              </Button>
              <Button variant="primary" onClick={() => router.push('/jobs')}>
                View All Jobs
              </Button>
            </div>
          </div>
        </Card>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button variant="ghost" onClick={() => router.back()}>
            <Icon name="arrow-left" size="sm" className="mr-2" />
            Back
          </Button>
          <div>
            <h1 className="text-2xl font-bold text-gray-900">{job.title}</h1>
            <p className="text-gray-600">{job.department} • {job.location}</p>
          </div>
        </div>
        
        <div className="flex items-center space-x-3">
          <Badge variant={getStatusVariant(job.status)} size="lg">
            {job.status.charAt(0).toUpperCase() + job.status.slice(1)}
          </Badge>
          <Button variant="outline" onClick={handleEdit}>
            <Icon name="edit" size="sm" className="mr-2" />
            Edit
          </Button>
          <Button variant="danger" onClick={handleDelete}>
            <Icon name="trash" size="sm" className="mr-2" />
            Delete
          </Button>
        </div>
      </div>

      {/* Job Details */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Main Content */}
        <div className="lg:col-span-2 space-y-6">
          {/* Job Description */}
          <Card title="Job Description" padding="lg">
            <div className="max-w-none text-gray-700">
              <div 
                className="ql-editor"
                dangerouslySetInnerHTML={{ __html: job.description }} 
              />
            </div>
          </Card>

          {/* Requirements */}
          <Card title="Requirements" padding="lg">
            <div className="max-w-none text-gray-700">
              <div 
                className="ql-editor"
                dangerouslySetInnerHTML={{ __html: Array.isArray(job.requirements) ? job.requirements.join('') : job.requirements }} 
              />
            </div>
          </Card>
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* Job Info */}
          <Card title="Job Information" padding="lg">
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Job Type
                </label>
                <p className="text-gray-900 capitalize">{job.type}</p>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Location
                </label>
                <p className="text-gray-900">{job.location}</p>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Department
                </label>
                <p className="text-gray-900">{job.department}</p>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Posted Date
                </label>
                <p className="text-gray-900">{formatDateConsistent(job.postedDate)}</p>
              </div>
              
              {job.closingDate && (
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Closing Date
                  </label>
                  <p className="text-gray-900">{formatDateConsistent(job.closingDate)}</p>
                </div>
              )}
              
              {job.salary && (
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Salary Range
                  </label>
                  <p className="text-gray-900">
                    {job.salary.currency} {job.salary.min.toLocaleString()} - {job.salary.max.toLocaleString()}
                  </p>
                </div>
              )}
            </div>
          </Card>

          {/* Actions */}
          <Card title="Actions" padding="lg">
            <div className="space-y-3">
              <Button variant="primary" className="w-full" onClick={handleEdit}>
                <Icon name="edit" size="sm" className="mr-2" />
                Edit Job Posting
              </Button>
              <Button variant="outline" className="w-full" onClick={() => router.push('/jobs')}>
                <Icon name="list" size="sm" className="mr-2" />
                View All Jobs
              </Button>
              <Button variant="danger" className="w-full" onClick={handleDelete}>
                <Icon name="trash" size="sm" className="mr-2" />
                Delete Job
              </Button>
            </div>
          </Card>
        </div>
      </div>
    </div>
  );
}

// Add Quill styles for proper rendering
const quillStyles = `
  .ql-editor h1 {
    font-size: 2em;
    font-weight: bold;
    margin: 0.67em 0;
  }
  .ql-editor h2 {
    font-size: 1.5em;
    font-weight: bold;
    margin: 0.83em 0;
  }
  .ql-editor h3 {
    font-size: 1.17em;
    font-weight: bold;
    margin: 1em 0;
  }
  .ql-editor ol {
    list-style-type: decimal;
    padding-left: 1.5em;
    margin: 1em 0;
  }
  .ql-editor ul {
    list-style-type: disc;
    padding-left: 1.5em;
    margin: 1em 0;
  }
  .ql-editor li {
    margin: 0.5em 0;
  }
  .ql-editor strong {
    font-weight: bold;
  }
  .ql-editor em {
    font-style: italic;
  }
  .ql-editor u {
    text-decoration: underline;
  }
`;

// Inject styles
if (typeof document !== 'undefined') {
  const styleElement = document.createElement('style');
  styleElement.textContent = quillStyles;
  document.head.appendChild(styleElement);
}
