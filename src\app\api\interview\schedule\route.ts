import { NextRequest, NextResponse } from "next/server";
import axios from "axios";
import { DynamoDBtable } from "@/lib/db_actions";
import { v4 as uuidv4 } from "uuid";

const InterviewScheduleTable = new DynamoDBtable("interview_schedule");
const CandidateTable = new DynamoDBtable("LCPCandidate");

export async function GET(req: NextRequest) {
  try {
    const { searchParams } = new URL(req.url);
    const candidateId = searchParams.get('candidate_id');
    const level = searchParams.get('level');

    if (!candidateId) {
      return NextResponse.json(
        { error: "candidate_id is required" },
        { status: 400 }
      );
    }

    // Fetch scheduled interviews for the candidate
    const response = await axios.get(
      `${process.env.NODE_RED_URL}/getScheduledInterviews`,
      {
        params: { candidate_id: candidateId, level },
      }
    );

    const scheduledInterviews = response.data || [];
    
    return NextResponse.json({
      candidate_id: candidateId,
      level,
      scheduled_interviews: scheduledInterviews,
      count: scheduledInterviews.length,
    });
  } catch (error) {
    console.error("Scheduled interviews fetch error:", error);
    
    if (axios.isAxiosError(error)) {
      return NextResponse.json(
        {
          message: "Failed to fetch scheduled interviews",
          error: error.message,
        },
        { status: error.response?.status || 500 }
      );
    }
    
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

export async function POST(req: NextRequest) {
  try {
    const body = await req.json();
    const {
      candidate_id,
      interviewer_id,
      interviewer_name,
      level,
      scheduled_date,
      scheduled_time,
      timezone = 'Asia/Kolkata',
      notes,
    } = body;

    // Validate required fields
    const requiredFields = [
      'candidate_id',
      'interviewer_id',
      'interviewer_name',
      'level',
      'scheduled_date',
      'scheduled_time',
    ];

    for (const field of requiredFields) {
      if (!body[field]) {
        return NextResponse.json(
          { error: `${field} is required` },
          { status: 400 }
        );
      }
    }

    // Generate unique ID for the scheduled interview
    const scheduleId = uuidv4();
    const currentTime = new Date().toISOString();

    // Create interview schedule record
    const scheduleData = {
      id: scheduleId,
      candidate_id,
      interviewer_id,
      interviewer_name,
      level,
      scheduled_date,
      scheduled_time,
      timezone,
      status: 'scheduled',
      created_at: currentTime,
      updated_at: currentTime,
      email_sent: false,
      notes: notes || '',
    };

    // Save to database
    const dbResponse = await axios.post(
      `${process.env.NODE_RED_URL}/createInterviewSchedule`,
      scheduleData
    );

    if (!dbResponse.data.success) {
      throw new Error('Failed to save interview schedule to database');
    }

    // Create calendar event
    let calendarEventId = null;
    try {
      const startDateTime = new Date(`${scheduled_date}T${scheduled_time}:00`);
      const endDateTime = new Date(startDateTime.getTime() + 60 * 60 * 1000); // 1 hour duration

      const calendarResponse = await fetch('/api/calendar/schedule', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          subject: `${level.toUpperCase()} Interview - Candidate Interview`,
          content: `Interview scheduled for candidate ID: ${candidate_id}\nLevel: ${level}\nInterviewer: ${interviewer_name}\nNotes: ${notes || 'No additional notes'}`,
          startDateTime: startDateTime.toISOString(),
          endDateTime: endDateTime.toISOString(),
          attendeeEmail: '<EMAIL>', // This should be fetched from interviewer data
          attendeeName: interviewer_name,
        }),
      });

      if (calendarResponse.ok) {
        const calendarData = await calendarResponse.json();
        calendarEventId = calendarData.id;
        
        // Update schedule record with calendar event ID
        await axios.put(
          `${process.env.NODE_RED_URL}/updateInterviewSchedule`,
          {
            id: scheduleId,
            calendar_event_id: calendarEventId,
          }
        );
      }
    } catch (calendarError) {
      console.warn("Calendar event creation failed:", calendarError.message);
      // Continue without calendar event - not critical
    }

    // Update interview record in main interview table
    const interviewData = {
      candidate_id,
      level,
      status: 'scheduled',
      interviewer: interviewer_name,
      start_time: `${scheduled_date}T${scheduled_time}:00.000Z`,
      end_time: null,
      decision: 'pending',
      interviewnotes: notes || '',
      is_ai_interview: level === 'level1',
      panel: interviewer_name,
      jobdescid: '', // This should be fetched from candidate data
      summary: `${level} interview scheduled with ${interviewer_name}`,
      timestamp: currentTime,
      video: null,
      schedule_id: scheduleId,
    };

    const interviewResponse = await fetch('/api/interview', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(interviewData),
    });

    if (!interviewResponse.ok) {
      console.warn("Failed to create interview record");
    }

    return NextResponse.json({
      success: true,
      message: "Interview scheduled successfully",
      data: {
        ...scheduleData,
        calendar_event_id: calendarEventId,
      },
    });
  } catch (error) {
    console.error("Interview scheduling error:", error);
    
    return NextResponse.json(
      {
        success: false,
        error: "Failed to schedule interview",
        details: error.message,
      },
      { status: 500 }
    );
  }
}

export async function PUT(req: NextRequest) {
  try {
    const body = await req.json();
    const { id, ...updateData } = body;

    if (!id) {
      return NextResponse.json(
        { error: "Schedule ID is required" },
        { status: 400 }
      );
    }

    // Update schedule in database
    const response = await axios.put(
      `${process.env.NODE_RED_URL}/updateInterviewSchedule`,
      {
        id,
        ...updateData,
        updated_at: new Date().toISOString(),
      }
    );

    return NextResponse.json({
      success: true,
      message: "Interview schedule updated successfully",
      data: response.data,
    });
  } catch (error) {
    console.error("Interview schedule update error:", error);
    
    return NextResponse.json(
      {
        success: false,
        error: "Failed to update interview schedule",
        details: error.message,
      },
      { status: 500 }
    );
  }
}

export async function DELETE(req: NextRequest) {
  try {
    const { searchParams } = new URL(req.url);
    const id = searchParams.get('id');

    if (!id) {
      return NextResponse.json(
        { error: "Schedule ID is required" },
        { status: 400 }
      );
    }

    // Cancel calendar event if exists
    try {
      const scheduleResponse = await axios.get(
        `${process.env.NODE_RED_URL}/getInterviewSchedule`,
        { params: { id } }
      );
      
      const scheduleData = scheduleResponse.data;
      if (scheduleData?.calendar_event_id) {
        await fetch(`/api/calendar/cancel/${scheduleData.calendar_event_id}`, {
          method: 'DELETE',
        });
      }
    } catch (calendarError) {
      console.warn("Calendar event cancellation failed:", calendarError.message);
    }

    // Delete from database
    const response = await axios.delete(
      `${process.env.NODE_RED_URL}/deleteInterviewSchedule`,
      { params: { id } }
    );

    return NextResponse.json({
      success: true,
      message: "Interview schedule cancelled successfully",
    });
  } catch (error) {
    console.error("Interview schedule cancellation error:", error);
    
    return NextResponse.json(
      {
        success: false,
        error: "Failed to cancel interview schedule",
        details: error.message,
      },
      { status: 500 }
    );
  }
}
