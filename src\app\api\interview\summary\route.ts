import { NextRequest, NextResponse } from "next/server";
import axios from "axios";
import { DynamoDBtable } from "@/lib/db_actions";

const InterviewTable = new DynamoDBtable("interview");

export async function GET(req: NextRequest) {
  try {
    const { searchParams } = new URL(req.url);
    const candidateId = searchParams.get('candidate_id');
    const level = searchParams.get('level');

    if (!candidateId) {
      return NextResponse.json(
        { error: "candidate_id is required" },
        { status: 400 }
      );
    }

    // Fetch external feedback data
    let externalFeedback = null;
    try {
      const externalResponse = await axios.get(
        `${process.env.NODE_RED_URL}/getInterviewFeedback`,
        {
          params: { candidate_id: candidateId, level },
          timeout: 5000, // 5 second timeout
        }
      );
      externalFeedback = externalResponse.data;
    } catch (externalError) {
      console.warn("External feedback API unavailable:", externalError.message);
      // Continue without external feedback - graceful degradation
    }

    // Fetch internal interview data
    const internalResponse = await axios.get(
      `${process.env.NODE_RED_URL}/getAIInterviewList`,
      {
        data: { candidate_id: candidateId },
      }
    );

    const internalData = internalResponse.data || [];
    
    // Filter by level if specified
    const filteredData = level 
      ? internalData.filter((interview: any) => interview.level === level)
      : internalData;

    // Combine internal and external data
    const combinedData = {
      candidate_id: candidateId,
      level,
      internal_interviews: filteredData,
      external_feedback: externalFeedback,
      has_external_feedback: !!externalFeedback,
      last_updated: new Date().toISOString(),
    };

    return NextResponse.json(combinedData);
  } catch (error) {
    console.error("Interview summary fetch error:", error);
    
    if (axios.isAxiosError(error)) {
      return NextResponse.json(
        {
          message: "Failed to fetch interview summary",
          error: error.message,
        },
        { status: error.response?.status || 500 }
      );
    }
    
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

export async function POST(req: NextRequest) {
  try {
    const body = await req.json();
    const { candidate_id, level, status, decision, feedback_data } = body;

    // Validate required fields
    if (!candidate_id || !level || !status) {
      return NextResponse.json(
        { error: "candidate_id, level, and status are required" },
        { status: 400 }
      );
    }

    // Update internal interview status
    const updateData = {
      candidate_id,
      level,
      status,
      decision: decision || 'pending',
      updated_at: new Date().toISOString(),
      ...feedback_data,
    };

    // Update external system if needed
    try {
      await axios.put(
        `${process.env.NODE_RED_URL}/updateInterviewStatus`,
        updateData
      );
    } catch (updateError) {
      console.warn("Failed to update external system:", updateError.message);
      // Continue with internal update even if external fails
    }

    // Update internal database
    const response = await fetch('/api/interview', {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(updateData),
    });

    if (!response.ok) {
      throw new Error('Failed to update internal interview data');
    }

    return NextResponse.json({
      success: true,
      message: "Interview status updated successfully",
      data: updateData,
    });
  } catch (error) {
    console.error("Interview status update error:", error);
    
    return NextResponse.json(
      {
        success: false,
        error: "Failed to update interview status",
        details: error.message,
      },
      { status: 500 }
    );
  }
}
