// Auth0 integration - commented out due to missing dependency
// import { getSession } from "@auth0/nextjs-auth0";
import axios from "axios";

const lcpInstance = axios.create({
  baseURL: "/api",
});

// lcpInstance.interceptors.request.use(async (config) => {
//   try {
//     const session = await getSession();
//     if (session && session.idToken) {
//       config.headers.Authorization = `Bearer ${session.idToken}`;
//     }
//     console.log(session, "session 11");
//   } catch (error) {
//     console.error("Error fetching Auth0 session", error);
//   }
//   return config;
// });

export default lcpInstance;
