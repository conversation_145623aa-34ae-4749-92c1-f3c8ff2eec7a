/**
 * An enumeration representing the status of an expense.
 *
 * This enum contains the following values:
 * - `NEW`: Indicates that the expense is new and has not been submitted yet.
 * - `SUBMITTED`: Indicates that the expense has been submitted for review.
 * - `APPROVED`: Indicates that the expense has been approved.
 * - `REJECTED`: Indicates that the expense has been rejected.
 * - `SETLED`: Indicates that the expense has been settled.
 */
export enum EXPENSE_STATUS {
  /** Indicates that the expense is new and has not been submitted yet. */
  NEW = "new",
  /** Indicates that the expense has been submitted for review. */
  SUBMITTED = "submitted",
  /** Indicates that the expense has been approved. */
  APPROVED = "approved",
  /** Indicates that the expense has been rejected. */
  REJECTED = "rejected",
  /** Indicates that the expense has been settled. */
  SETLED = "settled",
}

/**
 * An enumeration representing the roles of a user.
 *
 * This enum contains the following values:
 * - `ADMIN`: Indicates that the user is an admin.
 * - `EMPLOYEE`: Indicates that the user is an employee.
 */
export enum ROLES {
  /** Indicates that the user is an admin. */
  ADMIN = "Admin",
  /** Indicates that the user is an employee. */
  EMPLOYEE = "Employee",
  /** Indicates that the user is a hiring manager. */
  HIRING_MANAGER = "HiringManager",
  /** Indicates that the user is a interview panel. */

  INTERVIEW_PANEL = "InterviewPanel",
  /** Indicates that the user is a project manager. */

  PROJECT_MANAGER = "ProjectManager",
  /** Indicates that the user is a crm. */

  CRM = "CRM",
  includes = "includes",
}

export enum TRAVEL_STATUS {
  PENDING = "pending",
  APPROVED = "approved",
  REJECTED = "rejected",
}

export enum JOBS_STATUS {
  ACTIVE = "Active",
  ARCHIVE = "Archive",
}

export enum UIActionType {
  EXPENSE_APPROVAL = "expense.approval.ui",
  EXPENSE_ADMINMODE = "expense.adminmode.ui",
  JOBDESC_CREATE = "jobdesc.create.ui",
  JOBDESC_RESUME_UPLOAD = "jobdesc.candidate.resume.upload.ui",
  JOBDESC_INTERVIEW_DECISION_UPDATE = "jobdesc.candidate.interview.decision.update.ui",
  PROJECT_CREATE = "project.create.ui",
  PROJECT_TASK_EDIT = "project.task.edit.ui",
  EMPLOYEE_EDIT = "employee.edit.ui",
}

export enum Employee_Details {
  PersonalInformation = "Personal Information",
  ProjectInformation = "Project Information",
  TaskAssigned = "Task Assigned",
}

export enum ADMIN_MODE {
  ON = "ON",
  OFF = "OFF",
}
