import { NextRequest, NextResponse } from "next/server";
import axios from "axios";

export async function POST(request: NextRequest): Promise<NextResponse> {
  try {
    const requestHeaders = new Headers(request.headers);
    const email = requestHeaders.get("X-Auth-User-Email")!;
    const formData = await request.formData();
    const file = formData.get("file") as File | null;

    if (!file) {
      return NextResponse.json({ error: "File is required." }, { status: 400 });
    }

    // Prepare data to send to the external API
    const externalApiUrl = `${process.env.NODE_RED_URL}/resume-upload`;

    const formDataForApi = new FormData();
    formDataForApi.append("file", file);
    console.log(email, "<<<<<<<20");
    // Call the external API
    const response = await axios.post(externalApiUrl, formDataForApi, {
      headers: {
        "Content-Type": "multipart/form-data",
        context_user: email,
      },
    });

    // Log the response for debugging
    console.log("External API response:", response.data);

    // Return the response from the external API
    return NextResponse.json(response.data);
  } catch (error) {
    // Log the error details
    console.error(
      "Error calling external API:",
      (error as any).response?.data || (error as any).message,
    );

    // Return a more detailed error response
    return NextResponse.json(
      { error: (error as any).response?.data || "An unknown error occurred" },
      { status: 500 },
    );
  }
}
