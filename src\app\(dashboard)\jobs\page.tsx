'use client';

import React, { useState, useEffect } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import {
  <PERSON>ton,
  Card,
  SearchBar,
  FilterDropdown,
  DataTable,
  Badge,
  JobPostingCard,
  Icon,
  ConfirmDialog
} from '@/components';
import { Job } from '@/types';
import { formatDateConsistent } from '@/lib/utils';
import { JOBS_STATUS } from '@/Enums';

// Interface for job data from API
interface ApiJobData {
  jobdescid: string;
  jobtitle: string;
  responsibilities: string;
  requirement: string;
  skills: string;
  location: string;
  status: string;
  publishToExternalChannels: boolean;
  created_by: string;
  organization?: string;
  createdAt?: string;
  updatedAt?: string;
}

// Helper function to convert API data to Job interface
const convertApiJobToJob = (apiJob: ApiJobData): Job => {
  return {
    id: apiJob.jobdescid,
    title: apiJob.jobtitle,
    department: 'General', // Default since not in API
    location: apiJob.location,
    type: 'full-time', // Default since not in API
    status: apiJob.status.toLowerCase() as 'active' | 'paused' | 'closed' | 'draft',
    description: apiJob.responsibilities,
    requirements: apiJob.requirement.split(',').map(req => req.trim()),
    postedDate: apiJob.createdAt ? new Date(apiJob.createdAt) : new Date(),
    closingDate: undefined, // Not available in API
    salary: undefined, // Not available in API
  };
};

export default function JobsPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [searchQuery, setSearchQuery] = useState('');
  const [statusFilters, setStatusFilters] = useState<string[]>([]);
  const [departmentFilters, setDepartmentFilters] = useState<string[]>([]);
  const [jobs, setJobs] = useState<Job[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [deleteLoading, setDeleteLoading] = useState<string | null>(null);
  const [deleteDialog, setDeleteDialog] = useState<{
    isOpen: boolean;
    jobId: string | null;
    jobTitle: string;
  }>({
    isOpen: false,
    jobId: null,
    jobTitle: '',
  });

  // Fetch jobs from API
  const fetchJobs = async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await fetch('/api/jobDescription?Status=Active', {
        headers: {
          'X-Auth-User-Organization': 'demo-org',
          'X-Auth-User-Email': '<EMAIL>',
        },
      });

      if (!response.ok) {
        throw new Error('Failed to fetch jobs');
      }

      const data = await response.json();

      // Convert API data to Job interface
      const jobsData = Array.isArray(data) ? data : (data.data || []);
      const convertedJobs = jobsData.map((apiJob: ApiJobData) => convertApiJobToJob(apiJob));

      setJobs(convertedJobs);
    } catch (err) {
      console.error('Error fetching jobs:', err);
      setError(err instanceof Error ? err.message : 'Failed to fetch jobs');
    } finally {
      setLoading(false);
    }
  };

  // Load jobs on component mount and when refresh param is present
  useEffect(() => {
    fetchJobs();
  }, []);

  // Handle refresh parameter
  useEffect(() => {
    const refresh = searchParams.get('refresh');
    if (refresh === 'true') {
      fetchJobs();
      // Remove the refresh parameter from URL
      const newUrl = new URL(window.location.href);
      newUrl.searchParams.delete('refresh');
      window.history.replaceState({}, '', newUrl.toString());
    }
  }, [searchParams]);

  // Show delete confirmation dialog
  const showDeleteConfirmation = (job: Job) => {
    setDeleteDialog({
      isOpen: true,
      jobId: job.id,
      jobTitle: job.title,
    });
  };

  // Close delete confirmation dialog
  const closeDeleteConfirmation = () => {
    setDeleteDialog({
      isOpen: false,
      jobId: null,
      jobTitle: '',
    });
  };

  // Delete job function
  const handleDeleteJob = async () => {
    const { jobId } = deleteDialog;
    if (!jobId) return;

    try {
      setDeleteLoading(jobId);

      const response = await fetch(`/api/jobDescription?jobdescid=${jobId}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        throw new Error('Failed to delete job');
      }

      // Remove job from local state
      setJobs(prevJobs => prevJobs.filter(job => job.id !== jobId));

      // Close dialog and show success message
      closeDeleteConfirmation();
      alert('Job deleted successfully!');
    } catch (err) {
      console.error('Error deleting job:', err);
      alert('Failed to delete job. Please try again.');
    } finally {
      setDeleteLoading(null);
    }
  };

  // View job details
  const handleViewJob = (job: Job) => {
    router.push(`/jobs/${job.id}`);
  };

  // Edit job
  const handleEditJob = (job: Job) => {
    router.push(`/jobs/${job.id}/edit`);
  };

  // Calculate dynamic filter options based on current jobs
  const statusOptions = [
    {
      value: 'active',
      label: 'Active',
      count: jobs.filter(job => job.status === 'active').length
    },
    {
      value: 'paused',
      label: 'Paused',
      count: jobs.filter(job => job.status === 'paused').length
    },
    {
      value: 'closed',
      label: 'Closed',
      count: jobs.filter(job => job.status === 'closed').length
    },
    {
      value: 'draft',
      label: 'Draft',
      count: jobs.filter(job => job.status === 'draft').length
    }
  ];

  const departmentOptions = [
    {
      value: 'engineering',
      label: 'Engineering',
      count: jobs.filter(job => job.department.toLowerCase() === 'engineering').length
    },
    {
      value: 'design',
      label: 'Design',
      count: jobs.filter(job => job.department.toLowerCase() === 'design').length
    },
    {
      value: 'general',
      label: 'General',
      count: jobs.filter(job => job.department.toLowerCase() === 'general').length
    }
  ];

  const tableColumns = [
    { key: 'title', title: 'Job Title', sortable: true },
    { key: 'department', title: 'Department', sortable: true },
    { key: 'location', title: 'Location', sortable: true },
    {
      key: 'status',
      title: 'Status',
      render: (value: string) => {
        const getStatusVariant = (status: string) => {
          switch (status.toLowerCase()) {
            case 'active': return 'success';
            case 'paused': return 'warning';
            case 'closed': return 'danger';
            case 'draft': return 'default';
            default: return 'default';
          }
        };
        return (
          <Badge variant={getStatusVariant(value)}>
            {value.charAt(0).toUpperCase() + value.slice(1)}
          </Badge>
        );
      }
    },
    { key: 'postedDate', title: 'Posted Date', sortable: true }
  ];

  const tableData = jobs.map(job => ({
    ...job,
    postedDate: formatDateConsistent(job.postedDate)
  }));

  const handleCreateJob = () => {
    router.push('/jobs/create');
  };

  // Loading state
  if (loading) {
    return (
      <div className="space-y-6">
        <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-4">
          <div>
            <h1 className="text-xl sm:text-2xl font-bold text-gray-900">Job Postings</h1>
            <p className="text-gray-600">Manage and track your job openings</p>
          </div>
          <Button variant="primary" onClick={handleCreateJob} className="w-full sm:w-auto">
            Post New Job
          </Button>
        </div>

        <div className="flex items-center justify-center py-12">
          <div className="flex items-center space-x-2">
            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
            <span className="text-gray-600">Loading jobs...</span>
          </div>
        </div>
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div className="space-y-6">
        <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-4">
          <div>
            <h1 className="text-xl sm:text-2xl font-bold text-gray-900">Job Postings</h1>
            <p className="text-gray-600">Manage and track your job openings</p>
          </div>
          <Button variant="primary" onClick={handleCreateJob} className="w-full sm:w-auto">
            Post New Job
          </Button>
        </div>

        <Card padding="lg">
          <div className="text-center py-8">
            <Icon name="alert" size="xl" className="text-red-500 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">Error Loading Jobs</h3>
            <p className="text-gray-600 mb-4">{error}</p>
            <Button variant="primary" onClick={fetchJobs}>
              Try Again
            </Button>
          </div>
        </Card>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header Actions */}
      <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-4">
        <div>
          <h1 className="text-xl sm:text-2xl font-bold text-gray-900">Job Postings</h1>
          <p className="text-gray-600">
            Manage and track your job openings ({jobs.length} total)
          </p>
        </div>
        <Button variant="primary" onClick={handleCreateJob} className="w-full sm:w-auto">
          Post New Job
        </Button>
      </div>

      {/* Search and Filters */}
      <Card padding="md">
        <div className="space-y-4">
          <SearchBar
            placeholder="Search jobs by title, department, or location..."
            value={searchQuery}
            onChange={setSearchQuery}
            onSearch={(query) => console.log('Search:', query)}
          />
          
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
            <FilterDropdown
              title="Status"
              options={statusOptions}
              selectedValues={statusFilters}
              onChange={setStatusFilters}
              placeholder="Filter by status"
            />
            <FilterDropdown
              title="Department"
              options={departmentOptions}
              selectedValues={departmentFilters}
              onChange={setDepartmentFilters}
              placeholder="Filter by department"
            />
          </div>
        </div>
      </Card>

      {/* Job Cards Grid */}
      <div>
        <h2 className="text-lg font-semibold text-gray-900 mb-4">
          Active Postings ({jobs.filter(job => job.status === 'active').length})
        </h2>
        {jobs.length === 0 ? (
          <Card padding="lg">
            <div className="text-center py-8">
              <Icon name="plus" size="xl" className="text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">No Jobs Posted Yet</h3>
              <p className="text-gray-600 mb-4">
                Get started by creating your first job posting to attract top talent.
              </p>
              <Button variant="primary" onClick={handleCreateJob}>
                Post Your First Job
              </Button>
            </div>
          </Card>
        ) : (
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {jobs.map((job) => (
              <div key={job.id} className="relative">
                <JobPostingCard
                  job={job}
                  onView={() => handleViewJob(job)}
                  onEdit={() => handleEditJob(job)}
                  onDelete={() => showDeleteConfirmation(job)}
                />
                {deleteLoading === job.id && (
                  <div className="absolute inset-0 bg-white bg-opacity-75 flex items-center justify-center rounded-lg">
                    <div className="flex items-center space-x-2">
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-red-600"></div>
                      <span className="text-sm text-gray-600">Deleting...</span>
                    </div>
                  </div>
                )}
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Jobs Table */}
      <div>
        <h2 className="text-lg font-semibold text-gray-900 mb-4">All Jobs</h2>
        <Card padding="none">
          <DataTable
            columns={tableColumns}
            data={tableData}
            onSort={(key, direction) => console.log('Sort:', key, direction)}
            onRowClick={(row) => handleViewJob(row as Job)}
            emptyMessage="No jobs found matching your criteria"
          />
        </Card>
      </div>

      {/* Delete Confirmation Dialog */}
      <ConfirmDialog
        isOpen={deleteDialog.isOpen}
        onClose={closeDeleteConfirmation}
        onConfirm={handleDeleteJob}
        title="Delete Job Posting"
        message={`Are you sure you want to delete "${deleteDialog.jobTitle}"? This action cannot be undone and will remove all associated data.`}
        confirmText="Delete Job"
        cancelText="Cancel"
        variant="danger"
        loading={deleteLoading === deleteDialog.jobId}
      />
    </div>
  );
}
