'use client';

import React from 'react';
import { 
  Card, 
  Button,
  Badge,
  DataTable,
  Icon
} from '@/components';

export default function CalendarPage() {
  const [selectedDate, setSelectedDate] = React.useState(new Date());
  const [viewMode, setViewMode] = React.useState<'month' | 'week' | 'day'>('month');

  // Sample interview data
  const upcomingInterviews = [
    {
      id: '1',
      candidateName: '<PERSON>',
      position: 'Senior Frontend Developer',
      type: 'Technical Interview',
      date: '2024-01-25',
      time: '10:00 AM',
      duration: '60 min',
      interviewer: '<PERSON>',
      status: 'scheduled'
    },
    {
      id: '2',
      candidateName: '<PERSON>',
      position: 'Backend Engineer',
      type: 'Final Interview',
      date: '2024-01-25',
      time: '2:00 PM',
      duration: '45 min',
      interviewer: '<PERSON>',
      status: 'confirmed'
    },
    {
      id: '3',
      candidateName: '<PERSON>',
      position: 'UX Designer',
      type: 'Portfolio Review',
      date: '2024-01-26',
      time: '11:00 AM',
      duration: '90 min',
      interviewer: '<PERSON>',
      status: 'scheduled'
    }
  ];

  const tableColumns = [
    { key: 'candidateName', title: 'Candidate', sortable: true },
    { key: 'position', title: 'Position', sortable: true },
    { key: 'type', title: 'Interview Type', sortable: true },
    { key: 'date', title: 'Date', sortable: true },
    { key: 'time', title: 'Time', sortable: true },
    { key: 'interviewer', title: 'Interviewer', sortable: true },
    { 
      key: 'status', 
      title: 'Status',
      render: (value: string) => (
        <Badge variant={value === 'confirmed' ? 'success' : 'warning'}>
          {value}
        </Badge>
      )
    }
  ];

  const handleScheduleInterview = () => {
    console.log('Schedule new interview');
  };

  const handleViewChange = (mode: 'month' | 'week' | 'day') => {
    setViewMode(mode);
  };

  const todaysInterviews = upcomingInterviews.filter(
    interview => interview.date === '2024-01-25'
  );

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-4">
        <div>
          <h1 className="text-xl sm:text-2xl font-bold text-gray-900">Calendar</h1>
          <p className="text-gray-600">Schedule and manage interviews</p>
        </div>
        <Button variant="primary" onClick={handleScheduleInterview} className="w-full sm:w-auto">
          Schedule Interview
        </Button>
      </div>

      {/* Calendar Controls */}
      <Card padding="md">
        <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-4">
          <div className="flex flex-col sm:flex-row sm:items-center space-y-2 sm:space-y-0 sm:space-x-4">
            <h2 className="text-lg font-semibold text-gray-900">
              January 2024
            </h2>
            <div className="flex space-x-1">
              <Button
                variant={viewMode === 'month' ? 'primary' : 'outline'}
                size="sm"
                onClick={() => handleViewChange('month')}
              >
                Month
              </Button>
              <Button
                variant={viewMode === 'week' ? 'primary' : 'outline'}
                size="sm"
                onClick={() => handleViewChange('week')}
              >
                Week
              </Button>
              <Button
                variant={viewMode === 'day' ? 'primary' : 'outline'}
                size="sm"
                onClick={() => handleViewChange('day')}
              >
                Day
              </Button>
            </div>
          </div>

          <div className="flex items-center justify-center sm:justify-end space-x-2">
            <Button variant="ghost" size="sm">
              <Icon name="chevronLeft" size="sm" />
            </Button>
            <Button variant="outline" size="sm">
              Today
            </Button>
            <Button variant="ghost" size="sm">
              <Icon name="chevronRight" size="sm" />
            </Button>
          </div>
        </div>
      </Card>

      {/* Calendar Grid */}
      <Card padding="lg">
        <div className="h-96 flex items-center justify-center bg-gray-50 rounded-lg">
          <div className="text-center">
            <div className="text-gray-400 mb-2">📅</div>
            <p className="text-gray-600">Calendar view would go here</p>
            <p className="text-sm text-gray-500">Integration with calendar library needed</p>
          </div>
        </div>
      </Card>

      {/* Today's Schedule */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <div className="lg:col-span-2">
          <h2 className="text-lg font-semibold text-gray-900 mb-4">All Upcoming Interviews</h2>
          <DataTable
            columns={tableColumns}
            data={upcomingInterviews}
            onSort={(key, direction) => console.log('Sort:', key, direction)}
            onRowClick={(row) => console.log('Row clicked:', row)}
          />
        </div>

        <div>
          <h2 className="text-lg font-semibold text-gray-900 mb-4">Today's Schedule</h2>
          <Card padding="md">
            {todaysInterviews.length === 0 ? (
              <div className="text-center py-8">
                <Icon name="calendar" size="lg" className="mx-auto text-gray-400 mb-2" />
                <p className="text-gray-600">No interviews scheduled for today</p>
              </div>
            ) : (
              <div className="space-y-4">
                {todaysInterviews.map((interview) => (
                  <div key={interview.id} className="border-l-4 border-blue-500 pl-4 py-2">
                    <div className="flex justify-between items-start">
                      <div>
                        <p className="font-medium text-gray-900">{interview.candidateName}</p>
                        <p className="text-sm text-gray-600">{interview.position}</p>
                        <p className="text-sm text-gray-500">{interview.type}</p>
                      </div>
                      <Badge variant={interview.status === 'confirmed' ? 'success' : 'warning'} size="sm">
                        {interview.status}
                      </Badge>
                    </div>
                    <div className="mt-2 flex items-center space-x-4 text-sm text-gray-500">
                      <span className="flex items-center">
                        <Icon name="clock" size="sm" className="mr-1" />
                        {interview.time}
                      </span>
                      <span>{interview.duration}</span>
                    </div>
                    <p className="text-sm text-gray-600 mt-1">
                      Interviewer: {interview.interviewer}
                    </p>
                  </div>
                ))}
              </div>
            )}
          </Card>

          {/* Quick Actions */}
          <Card title="Quick Actions" padding="md" className="mt-6">
            <div className="space-y-3">
              <Button variant="outline" className="w-full justify-start">
                <Icon name="plus" size="sm" className="mr-2" />
                Schedule Interview
              </Button>
              <Button variant="outline" className="w-full justify-start">
                <Icon name="calendar" size="sm" className="mr-2" />
                View Availability
              </Button>
              <Button variant="outline" className="w-full justify-start">
                <Icon name="mail" size="sm" className="mr-2" />
                Send Reminders
              </Button>
            </div>
          </Card>
        </div>
      </div>
    </div>
  );
}
