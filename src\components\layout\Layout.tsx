'use client';

import React, { useState, useEffect } from 'react';
import { usePathname } from 'next/navigation';
import { cn } from '@/lib/utils';
import Sidebar from './Sidebar';
import Header from './Header';
import { NavigationItem } from '@/types';

interface LayoutProps {
  children: React.ReactNode;
  title?: string;
  showSearch?: boolean;
  onSearch?: (query: string) => void;
  headerActions?: React.ReactNode;
  className?: string;
}

const Layout: React.FC<LayoutProps> = ({
  children,
  title,
  showSearch = true,
  onSearch,
  headerActions,
  className,
}) => {
  const pathname = usePathname();
  const [isSidebarOpen, setIsSidebarOpen] = useState(false);

  const toggleSidebar = () => {
    setIsSidebarOpen(!isSidebarOpen);
  };

  const closeSidebar = () => {
    setIsSidebarOpen(false);
  };

  const [jobsCount, setJobsCount] = useState<number | null>(null);

  const fetchJobsCount = async () => {
    try {
      const response = await fetch('/api/jobDescription?Status=Active');
      if (!response.ok) throw new Error('Failed to fetch jobs');
      const data = await response.json();
      const jobsData = Array.isArray(data) ? data : (data.data || []);
      setJobsCount(jobsData.length);
    } catch (error) {
      setJobsCount(null);
    }
  };

  useEffect(() => {
    fetchJobsCount();
  }, []);

  // Listen for job creation events
  useEffect(() => {
    const handleJobCreated = () => {
      fetchJobsCount();
    };

    // Listen for custom event when job is created
    window.addEventListener('jobCreated', handleJobCreated);
    
    // Also listen for URL changes that might indicate job creation
    const handleUrlChange = () => {
      if (pathname.includes('/jobs') && pathname !== '/jobs') {
        // Small delay to ensure the job is saved
        setTimeout(fetchJobsCount, 1000);
      }
    };

    window.addEventListener('popstate', handleUrlChange);

    return () => {
      window.removeEventListener('jobCreated', handleJobCreated);
      window.removeEventListener('popstate', handleUrlChange);
    };
  }, [pathname]);

  // Expose refresh function globally
  useEffect(() => {
    (window as any).refreshJobsCount = fetchJobsCount;
    return () => {
      delete (window as any).refreshJobsCount;
    };
  }, []);

  const sidebarItems: NavigationItem[] = [
    {
      id: 'dashboard',
      label: 'Dashboard',
      icon: 'dashboard',
      href: '/dashboard',
      description: 'Overview and key metrics'
    },
    {
      id: 'jobs',
      label: 'Jobs',
      icon: 'jobs',
      href: '/jobs',
      badge: jobsCount !== null ? String(jobsCount) : undefined,
      description: 'Manage job postings'
    },
    {
      id: 'candidates',
      label: 'Candidates',
      icon: 'candidates',
      href: '/candidates',
      description: 'View and manage candidates'
    },
    {
      id: 'resume-analysis',
      label: 'Resume Analysis',
      icon: 'analytics',
      href: '/resume-analysis',
      description: 'AI-powered resume analysis'
    },
    {
      id: 'analytics',
      label: 'Analytics',
      icon: 'analytics',
      href: '/analytics',
      description: 'Recruitment analytics and reports'
    },
    {
      id: 'calendar',
      label: 'Calendar',
      icon: 'calendar',
      href: '/calendar',
      description: 'Schedule and manage interviews'
    },
    {
      id: 'profile',
      label: 'Profile',
      icon: 'settings',
      href: '/profile',
      description: 'Your profile settings'
    },
    {
      id: 'settings',
      label: 'Settings',
      icon: 'settings',
      href: '/settings',
      description: 'Application settings'
    },
    {
      id: 'logout',
      label: 'Logout',
      icon: 'logout',
      href: '/api/auth/logout',
      description: 'Log out of your account'
      
    }
  ];

  // Get page title based on current route
  const getPageTitle = () => {
    if (title) return title;

    const currentItem = sidebarItems.find(item => {
      if (item.href === '/dashboard' && pathname === '/dashboard') {
        return true;
      }
      if (item.href !== '/dashboard' && pathname.startsWith(item.href)) {
        return true;
      }
      return false;
    });

    return currentItem?.label || 'AI RecruitPro';
  };

  return (
    <div className="flex h-screen bg-gray-50">
      {/* Mobile Sidebar Overlay */}
      {isSidebarOpen && (
        <div
          className="fixed inset-0 z-40 bg-black bg-opacity-50 lg:hidden"
          onClick={closeSidebar}
        />
      )}

      {/* Sidebar */}
      <div className={cn(
        "fixed inset-y-0 left-0 z-50 w-64 transform transition-transform duration-300 ease-in-out lg:translate-x-0 lg:static lg:inset-0",
        isSidebarOpen ? "translate-x-0" : "-translate-x-full"
      )}>
        <Sidebar items={sidebarItems} onItemClick={closeSidebar} />
      </div>

      {/* Main Content Area */}
      <div className="flex-1 flex flex-col overflow-hidden lg:ml-0">
        {/* Header */}
        <Header
          title={getPageTitle()}
          showSearch={showSearch}
          onSearch={onSearch}
          actions={headerActions}
          onMenuClick={toggleSidebar}
          showMenuButton={true}
        />

        {/* Page Content */}
        <main className={cn('flex-1 overflow-auto p-4 sm:p-6', className)}>
          {children}
        </main>
      </div>
    </div>
  );
};

export default Layout;
