export async function sendEmail(accessToken: string) {
  const emailData = {
    message: {
      subject: "Hello from Next.js",
      body: {
        contentType: "Text",
        content: "This is a test email from Next.js using Microsoft Graph API!",
      },
      toRecipients: [
        {
          emailAddress: {
            address: "sridhar.chandra<PERSON><PERSON>@neartekpod.com",
          },
        },
      ],
    },
  };

  const response = await fetch("https://graph.microsoft.com/v1.0/me/sendMail", {
    method: "POST",
    headers: {
      Authorization: `Bearer ${accessToken}`,
      "Content-Type": "application/json",
    },
    body: JSON.stringify(emailData),
  });

  if (response.ok) {
    console.log("Email sent successfully!");
  } else {
    console.error("Failed to send email:", await response.json());
  }
}
