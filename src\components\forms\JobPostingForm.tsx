'use client';

import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>, <PERSON>, FormField } from '@/components';
import { JOBS_STATUS } from '@/Enums';
import dynamic from 'next/dynamic';
import type { QuillEditorProps } from './QuillEditor';

const QuillEditor = dynamic(() => import('./QuillEditor'), { ssr: false }) as React.ComponentType<QuillEditorProps>;

interface JobFormData {
  jobtitle: string;
  responsibilities: string;
  requirement: string;
  skills: string;
  location: string;
  status: string;
  publishToExternalChannels: boolean;
  // Additional fields for better UX
  department: string;
  jobType: string;
  salaryMin: string;
  salaryMax: string;
  salaryCurrency: string;
  closingDate: string;
}

interface JobPostingFormProps {
  onSubmit: (data: JobFormData) => Promise<void>;
  onCancel: () => void;
  loading?: boolean;
  initialData?: JobFormData;
  isEditing?: boolean;
}

const JobPostingForm: React.FC<JobPostingFormProps> = ({
  onSubmit,
  onCancel,
  loading = false,
  initialData,
  isEditing = false,
}) => {
  const [formData, setFormData] = useState<JobFormData>(
    initialData || {
      jobtitle: '',
      responsibilities: '',
      requirement: '',
      skills: '',
      location: '',
      status: JOBS_STATUS.ACTIVE,
      publishToExternalChannels: false,
      department: '',
      jobType: 'full-time',
      salaryMin: '',
      salaryMax: '',
      salaryCurrency: 'USD',
      closingDate: '',
    }
  );

  // Update form data when initialData changes
  useEffect(() => {
    if (initialData) {
      setFormData(initialData);
    }
  }, [initialData]);

  const [errors, setErrors] = useState<Partial<JobFormData>>({});

  const handleFieldChange = (name: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
    
    // Clear error when user starts typing
    if (errors[name as keyof JobFormData]) {
      setErrors(prev => ({
        ...prev,
        [name]: undefined
      }));
    }
  };

  const handleCheckboxChange = (name: string, checked: boolean) => {
    setFormData(prev => ({
      ...prev,
      [name]: checked
    }));
  };

  const stripHtml = (html: string) => html.replace(/<[^>]*>/g, '').replace(/&nbsp;/g, '').trim();

  const validateForm = (): boolean => {
    const newErrors: Partial<JobFormData> = {};

    if (!formData.jobtitle.trim()) {
      newErrors.jobtitle = 'Job title is required';
    }

    if (!stripHtml(formData.responsibilities)) {
      newErrors.responsibilities = 'Job responsibilities are required';
    }

    if (!stripHtml(formData.requirement)) {
      newErrors.requirement = 'Job requirements are required';
    }

    if (!stripHtml(formData.skills)) {
      newErrors.skills = 'Required skills are required';
    }

    if (!formData.location.trim()) {
      newErrors.location = 'Location is required';
    }

    if (!formData.department.trim()) {
      newErrors.department = 'Department is required';
    }

    // Validate salary if provided
    if (formData.salaryMin && formData.salaryMax) {
      const minSalary = parseFloat(formData.salaryMin);
      const maxSalary = parseFloat(formData.salaryMax);
      
      if (isNaN(minSalary) || minSalary < 0) {
        newErrors.salaryMin = 'Please enter a valid minimum salary';
      }
      
      if (isNaN(maxSalary) || maxSalary < 0) {
        newErrors.salaryMax = 'Please enter a valid maximum salary';
      }
      
      if (!isNaN(minSalary) && !isNaN(maxSalary) && minSalary >= maxSalary) {
        newErrors.salaryMax = 'Maximum salary must be greater than minimum salary';
      }
    }

    // Validate closing date if provided
    if (formData.closingDate) {
      const closingDate = new Date(formData.closingDate);
      const today = new Date();
      today.setHours(0, 0, 0, 0);
      
      if (closingDate <= today) {
        newErrors.closingDate = 'Closing date must be in the future';
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    try {
      await onSubmit(formData);
    } catch (error) {
      console.error('Form submission error:', error);
    }
  };

  const departmentOptions = [
    { value: '', label: 'Select Department' },
    { value: 'Engineering', label: 'Engineering' },
    { value: 'Design', label: 'Design' },
    { value: 'Marketing', label: 'Marketing' },
    { value: 'Sales', label: 'Sales' },
    { value: 'HR', label: 'Human Resources' },
    { value: 'Finance', label: 'Finance' },
    { value: 'Operations', label: 'Operations' },
    { value: 'Customer Success', label: 'Customer Success' },
  ];

  const jobTypeOptions = [
    { value: 'full-time', label: 'Full-time' },
    { value: 'part-time', label: 'Part-time' },
    { value: 'contract', label: 'Contract' },
    { value: 'internship', label: 'Internship' },
  ];

  const statusOptions = [
    { value: JOBS_STATUS.ACTIVE, label: 'Active' },
    { value: JOBS_STATUS.ARCHIVE, label: 'Draft' },
  ];

  const currencyOptions = [
    { value: 'USD', label: 'USD ($)' },
    { value: 'EUR', label: 'EUR (€)' },
    { value: 'GBP', label: 'GBP (£)' },
    { value: 'CAD', label: 'CAD (C$)' },
  ];

  const locationOptions = [
    { value: '', label: 'Select Location' },
    { value: 'Chicago, Illinois, US', label: 'Chicago, Illinois, US' },
    { value: 'Trichy, Tamil Nadu, India', label: 'Trichy, Tamil Nadu, India' },
  ];

  return (
    <div className="max-w-4xl mx-auto">
      <Card title={isEditing ? "Edit Job Posting" : "Post New Job"} padding="lg">
        <form onSubmit={handleSubmit} className="space-y-6 text-black">
          {/* Basic Information Section */}
          <div>
            <h3 className="text-lg font-medium text-gray-900 mb-4">Basic Information</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <FormField
                label="Job Title"
                name="jobtitle"
                type="text"
                value={formData.jobtitle}
                onChange={handleFieldChange}
                placeholder="e.g., Senior Frontend Developer"
                required
                error={errors.jobtitle}
              />
              
              <FormField
                label="Department"
                name="department"
                type="select"
                value={formData.department}
                onChange={handleFieldChange}
                options={departmentOptions}
                required
                error={errors.department}
              />
              
              <FormField
                label="Location"
                name="location"
                type="select"
                value={formData.location}
                onChange={handleFieldChange}
                options={locationOptions}
                required
                error={errors.location}
              />
              
              <FormField
                label="Job Type"
                name="jobType"
                type="select"
                value={formData.jobType}
                onChange={handleFieldChange}
                options={jobTypeOptions}
                required
              />
            </div>
          </div>

          {/* Job Details Section */}
          <div>
            <h3 className="text-lg font-medium text-gray-900 mb-4">Job Details</h3>
            <div className="space-y-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Job Responsibilities<span className="text-red-500">*</span></label>
                <QuillEditor
                  value={formData.responsibilities}
                  onChange={value => handleFieldChange('responsibilities', value)}
                  placeholder="Describe the main responsibilities and duties for this position..."
                />
                {errors.responsibilities && <div className="text-red-500 text-xs mt-1">{errors.responsibilities}</div>}
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Requirements<span className="text-red-500">*</span></label>
                <QuillEditor
                  value={formData.requirement}
                  onChange={value => handleFieldChange('requirement', value)}
                  placeholder="List the required qualifications, experience, and education..."
                />
                {errors.requirement && <div className="text-red-500 text-xs mt-1">{errors.requirement}</div>}
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Required Skills<span className="text-red-500">*</span></label>
                <QuillEditor
                  value={formData.skills}
                  onChange={value => handleFieldChange('skills', value)}
                  placeholder="List the technical skills and competencies required (e.g., React, TypeScript, Node.js)..."
                />
                {errors.skills && <div className="text-red-500 text-xs mt-1">{errors.skills}</div>}
              </div>
            </div>
          </div>

          {/* Compensation Section */}
          {/* <div>
            <h3 className="text-lg font-medium text-gray-900 mb-4">Compensation (Optional)</h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <FormField
                label="Minimum Salary"
                name="salaryMin"
                type="number"
                value={formData.salaryMin}
                onChange={handleFieldChange}
                placeholder="50000"
                error={errors.salaryMin}
              />
              
              <FormField
                label="Maximum Salary"
                name="salaryMax"
                type="number"
                value={formData.salaryMax}
                onChange={handleFieldChange}
                placeholder="80000"
                error={errors.salaryMax}
              />
              
              <FormField
                label="Currency"
                name="salaryCurrency"
                type="select"
                value={formData.salaryCurrency}
                onChange={handleFieldChange}
                options={currencyOptions}
              />
            </div>
          </div> */}

          {/* Additional Settings Section */}
          <div>
            <h3 className="text-lg font-medium text-gray-900 mb-4">Additional Settings</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <FormField
                label="Application Closing Date"
                name="closingDate"
                type="date"
                value={formData.closingDate}
                onChange={handleFieldChange}
                error={errors.closingDate}
                helpText="Leave empty if no specific closing date"
              />
              
              <FormField
                label="Status"
                name="status"
                type="select"
                value={formData.status}
                onChange={handleFieldChange}
                options={statusOptions}
                required
              />
            </div>
            
            <div className="mt-4">
              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={formData.publishToExternalChannels}
                  onChange={(e) => handleCheckboxChange('publishToExternalChannels', e.target.checked)}
                  className="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
                />
                <span className="ml-2 text-sm text-gray-700">
                  Publish to external job boards and channels
                </span>
              </label>
            </div>
          </div>

          {/* Form Actions */}
          <div className="flex justify-end space-x-4 pt-6 border-t border-gray-200">
            <Button
              type="button"
              variant="outline"
              onClick={onCancel}
              disabled={loading}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              variant="primary"
              loading={loading}
              disabled={loading}
            >
              {loading
                ? (isEditing ? 'Updating Job...' : 'Posting Job...')
                : (isEditing ? 'Update Job' : 'Post Job')
              }
            </Button>
          </div>
        </form>
      </Card>
    </div>
  );
};

export default JobPostingForm;
export type { JobFormData };
