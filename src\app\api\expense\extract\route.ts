import { searchParams } from "@/utils";
import axios from "axios";
import { NextRequest } from "next/server";

export async function POST(request: NextRequest): Promise<Response> {
  try {
    const requestHeaders = new Headers(request.headers);
    const user_id = requestHeaders.get("X-Auth-User-ID")!;
    const email = requestHeaders.get("X-Auth-User-Email")!;

    const formData = await request.formData();
    const FILE = formData.get("receipt") as File;
    console.log(FILE);
    let data = new FormData();
    data.append("receipt", FILE);

    let config = {
      method: "post",
      maxBodyLength: Infinity,
      url: `${process.env.NODE_RED_URL}/process_receipt`,
      headers: {
        context_user: email,
        user_name: requestHeaders.get("X-Auth-User-Name"),
      },
      data: data,
    };

    const node_data = await axios.request(config);
    console.log(node_data, "<<node_data>>");
    if (node_data.data?.fileuuid) {
      return Response.json(
        { ...node_data.data, message: "File uploaded" },
        {
          status: 200,
        },
      );
    } else {
      return Response.json(
        { message: "fileuuid missing" },
        {
          status: 400,
        },
      );
    }
  } catch (error) {
    return Response.json(
      { message: error },
      {
        status: 500,
      },
    );
  }
}

export async function GET(request: NextRequest): Promise<Response> {
  try {
    const requestHeaders = new Headers(request.headers);
    const user_id = requestHeaders.get("X-Auth-User-ID")!;
    const fileuuid = searchParams(request, "fileuuid");

    const response = await axios.get(
      `${process.env.NODE_RED_URL}/receipt_status`,
      {
        params: {
          fileuuid,
        },
        headers: {
          context_user: user_id,
        },
      },
    );
    return Response.json(
      { ...response.data },
      {
        status: 200,
      },
    );
  } catch (error) {
    return Response.json(
      { message: error },
      {
        status: 500,
      },
    );
  }
}
