// src/components/forms/QuillEditor.tsx
'use client';

import React, { useEffect, useRef } from 'react';
import Quill from 'quill';
import 'quill/dist/quill.snow.css';

export interface QuillEditorProps {
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
}

const QuillEditor: React.FC<QuillEditorProps> = ({ value, onChange, placeholder }) => {
  const editorRef = useRef<HTMLDivElement>(null);
  const quillRef = useRef<Quill | null>(null);

  useEffect(() => {
    if (editorRef.current && !quillRef.current) {
      quillRef.current = new Quill(editorRef.current, {
        theme: 'snow',
        placeholder,
      });
      quillRef.current.on('text-change', () => {
        onChange(quillRef.current!.root.innerHTML);
      });
      quillRef.current.root.innerHTML = value || '';
    }
    // Update value if changed externally
    if (quillRef.current && quillRef.current.root.innerHTML !== value) {
      quillRef.current.root.innerHTML = value || '';
    }
    // eslint-disable-next-line
  }, [editorRef, value]);

  return <div ref={editorRef} />;
};

export default QuillEditor; 