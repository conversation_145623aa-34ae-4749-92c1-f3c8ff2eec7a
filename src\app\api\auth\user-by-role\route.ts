import axios from "axios";

export async function GET(request: Request) {
  try {
    // let config = {
    //   method: "get",
    //   maxBodyLength: Infinity,
    //   url: "https://dev-bsz8g2ina5lrwfw3.us.auth0.com/api/v2/roles/rol_jyM9SBEh26ALIhgo/users",
    //   headers: {
    //     Accept: "application/json",
    //     Authorization: `Bearer ${process.env.AUTH0_TEST_TOKEN}`,
    //   },
    // };
    // let data;
    // const response = await axios.request(config);
    // data = response.data;
    return Response.json(
      {
        data: [
          {
            name: "<PERSON><PERSON>",
          },
          {
            name: "<PERSON>",
          },
          {
            name: "<PERSON>",
          },
          {
            name: "<PERSON><PERSON>",
          },
          {
            name: "<PERSON><PERSON><PERSON>",
          },
          {
            name: "<PERSON><PERSON>",
          },
          {
            name: "<PERSON><PERSON><PERSON>",
          },
          {
            name: "<PERSON>",
          },
          {
            name: "<PERSON>",
          },
          {
            name: "<PERSON><PERSON><PERSON><PERSON>",
          },
          {
            name: "<PERSON><PERSON><PERSON>",
          },
        ],
      },
      {
        status: 200,
      },
    );
  } catch (error) {
    return Response.json(
      { data: error },
      {
        status: 200,
      },
    );
  }
}
