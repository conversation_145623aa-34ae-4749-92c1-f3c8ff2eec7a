import { NextRequest } from "next/server";

/**
 * Approve a receipt in the expense system.
 *
 * @param {NextRequest} request - The incoming request
 * @returns {Promise<Response>} - The response to the request
 * @throws {Error} - An error if the request fails
 */
export async function PUT(request: NextRequest): Promise<Response> {
  try {
    let body = await request.json();
    const axios = require("axios");
    let data = JSON.stringify({
      expenseid: body?.expenseid,
      status: body?.status,
      expenseComment: body?.expenseComment,
    });

    let config = {
      method: "put",
      url: `${process.env.NODE_RED_URL}/receipt_approval`,
      headers: {
        "Content-Type": "application/json",
      },
      data: data,
    };

    const res = await axios.request(config);
    return Response.json(
      { ...res.data },
      {
        status: 200,
      },
    );
  } catch (error) {
    return Response.json(
      { message: error },
      {
        status: 500,
      },
    );
  }
}
