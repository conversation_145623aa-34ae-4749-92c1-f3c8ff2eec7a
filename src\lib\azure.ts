// Azure MSAL configuration - commented out due to missing dependency
// import { PublicClientApplication } from "@azure/msal-browser";

// const msalConfig = {
//   auth: {
//     clientId: "6d326449-00e7-4633-badc-54c3e52b5dc7",
//     authority:
//       "https://login.microsoftonline.com/fc36e42d-dc36-4608-b794-27d0298b8827",
//     redirectUri: "http://localhost:3000", // Your Redirect URL
//   },
// };

// const msalInstance = new PublicClientApplication(msalConfig);

// Sign-in and get token
export async function getAccessToken() {
  // const loginResponse = await msalInstance.loginPopup({
  //   scopes: ["https://graph.microsoft.com/Mail.Send"],
  // });
  // return loginResponse.accessToken;

  // Placeholder implementation
  return "demo-token";
}
