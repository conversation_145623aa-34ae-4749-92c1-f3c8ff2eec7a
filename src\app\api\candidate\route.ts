import axios from "axios";
import { NextRequest, NextResponse } from "next/server";
import { z } from "zod";

const employmentHistorySchema = z.object({
  role: z.string(),
  organization: z.string(),
  start_date: z.string(),
  end_date: z.string(),
});

const educationSchema = z.object({
  degree: z.string().min(1),
  start_date: z.string(),
  end_date: z.string(),
  field_of_study: z.string(),
  institution: z.string().min(1).optional(),
});

const resumeSchema = z.object({
  name: z.string(),
  email: z.string().email(),
  phone_number: z.string(),
  years_of_experience: z.string(),
  experience_summary: z.string(),
  achievements: z.array(z.string()),
  certifications: z.array(z.string()),
  education: z.array(educationSchema),
  employment_history: z.array(employmentHistorySchema),
  key_skills: z.array(z.string()),
  Resume_Explanation: z.string().optional(),
  Resume_Percentage: z.string().optional(),
  Job_id: z.string().uuid(),
  current_location: z.string(),
  flexible_with_relocation: z.string(),
  current_notice_period: z.string(),
  current_ctc: z.string(),
  expected_ctc: z.string(),
  reason_for_change: z.string().optional(),
  fileid: z.string(),
  candidateid: z.string().optional(),
  status: z.string().optional(),
  reference_mode: z.string().optional(),
  reference_person: z.string().optional(),
  Priority: z.union([z.number(), z.null()]).optional(), // Allow null priority
});

export type ResumeSchema = z.infer<typeof resumeSchema>;

export async function POST(request: NextRequest) {
  try {
    const BODY = await request.json();
    console.log("Request Body:", BODY);

    const user_id = request.headers.get("X-Auth-User-ID");
    console.log("user_id", user_id);

    const validatedBody: ResumeSchema = resumeSchema.parse({
      ...BODY,
      context_user: user_id,
    });

    // Ensure Priority defaults to 9999 if null or undefined
    const finalBody = {
      ...validatedBody,
      Priority: validatedBody.Priority ?? 9999,
    };
    console.log(finalBody, "finalBody");

    const { data } = await axios.post(`${process.env.NODE_RED_URL}/candidate`, {
      ...finalBody, // Use updated finalBody with Priority set
      context_user: user_id,
    });
    console.log("data", data);

    return Response.json({ data });
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { message: "Validation failed", errors: error.errors },
        { status: 400 },
      );
    }
    return Response.json({ error });
  }
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const id = searchParams.get("job_id");
    const candidateid = searchParams.get("candidateid");
    const status = searchParams.get("status");

    let record;

    if(!id && !candidateid && !status) {
        const response = await axios.get(
        `${process.env.NODE_RED_URL}/candidate-data`);
      record = response.data;
      console.log("record-----", record);
        return new NextResponse(
        JSON.stringify(record),
        {
          headers: { "Content-Type": "application/json" },
          status: 200,
        },
      );
    }

    if (id) {
      const response = await axios.get(
        `${process.env.NODE_RED_URL}/candidate-list`,
        {
          params: { id, status },
        },
      );
      record = response.data;
      console.log("record-----", record);

      // Sort by priority, treating null or undefined as highest value (9999)
      if (Array.isArray(record?.Items)) {
        record.Items.sort((a, b) => {
          const priorityA = a.Priority ?? 9999;
          const priorityB = b.Priority ?? 9999;
          return priorityA - priorityB;
        });
      }
    } else if (candidateid) {
      const [detailsResponse, ivDetailsResponse] = await Promise.all([
        axios.get(`${process.env.NODE_RED_URL}/details`, {
          params: { candidateid },
        }),
        axios.get(`${process.env.NODE_RED_URL}/getIVDetails`, {
          params: { candidate_id: candidateid },
        }),
      ]);

      record = detailsResponse.data;
      record.interview_eval_details = ivDetailsResponse.data;

      // Sort by priority if items exist
      if (record.items && Array.isArray(record.items)) {
        record.items.sort((a, b) => {
          const priorityA = a.Priority ?? 9999;
          const priorityB = b.Priority ?? 9999;
          return priorityA - priorityB;
        });
      }
    } else {
      return new NextResponse(
        JSON.stringify({ error: "job_id or candidateid is required" }),
        {
          headers: { "Content-Type": "application/json" },
          status: 400,
        },
      );
    }

    if (!record) {
      return new NextResponse(JSON.stringify({ error: "Record not found" }), {
        headers: { "Content-Type": "application/json" },
        status: 404,
      });
    }
    return new NextResponse(JSON.stringify(record), {
      headers: { "Content-Type": "application/json" },
    });
  } catch (error) {
    if (axios.isAxiosError(error)) {
      return new NextResponse(
        JSON.stringify({
          message: "Failed to fetch data from external API",
          error: error.message,
        }),
        { status: error.response?.status || 500 },
      );
    }

    return new NextResponse(
      JSON.stringify({ error: "Internal server error" }),
      {
        status: 500,
      },
    );
  }
}

export async function PUT(request: NextRequest) {
  try {
    // console.log("PUT----------------",body);
    const body = await request.json();

    console.log("PUT----------------", body);
    // const { id, ...data } = body;
    const { candidateid, Priority, status } = body;

    if(candidateid && status){
       const { data: responseData } = await axios.put(
      `${process.env.NODE_RED_URL}/candidateupdate-status`,
      {
        status,
        candidateid,
      },
    );

    console.log("API Response Data:", responseData);
    return NextResponse.json(responseData, { status: 200 });
    }



    if (!candidateid || Priority === undefined) {
      return NextResponse.json(
        { error: "Candidate ID and Priority are required" },
        { status: 400 },
      );
    }

    console.log("Payload to be sent:", { candidateid, Priority });

    const { data: responseData } = await axios.put(
      `${process.env.NODE_RED_URL}/candidateupdate`,
      {
        ...body,
        candidateid,
        Priority,
      },
    );

    console.log("API Response Data:", responseData);
    return NextResponse.json(responseData, { status: 200 });
  } catch (error) {
    if (axios.isAxiosError(error)) {
      console.error("Axios error:", error.response?.data || error.message);
      return NextResponse.json(
        {
          message: "External API error",
          error: error.response?.data || error.message,
        },
        { status: error.response?.status || 500 },
      );
    }

    console.error("Unexpected error:", error);
    return NextResponse.json(
      { message: "An unexpected error occurred", error: error },
      { status: 500 },
    );
  }
}

export async function DELETE(request: NextRequest) {
  try {
    const queryParams = new URL(request.url).searchParams;
    const candidateid = queryParams.get("candidateid");

    console.log("Candidate ID:", candidateid);
    if (!candidateid) {
      return NextResponse.json(
        { error: "Missing candidate ID" },
        { status: 400 },
      );
    }
    const response = await axios.delete(
      `${process.env.NODE_RED_URL}/can-list-delete`,
      {
        params: { candidateid },
      },
    );

    console.log("API Response from Node-RED:", response.data);
    return NextResponse.json({ message: "Deleted successfully" });
  } catch (error) {
    console.error("Error during DELETE:", error);
    return new Response(
      JSON.stringify({ error: "Could not delete candidate" }),
      { status: 500 },
    );
  }
}
