{"expense": [{"action": "expense.approval.ui", "roles": ["Admin"]}, {"action": "expense.adminmode.ui", "roles": ["Admin"]}], "jobdesc": [{"action": "jobdesc.create.ui", "roles": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "Employee"]}, {"action": "jobdesc.candidate.resume.upload.ui", "roles": ["<PERSON><PERSON><PERSON><PERSON><PERSON>"]}, {"action": "jobdesc.candidate.interview.decision.update.ui", "roles": ["Interview<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>"]}], "project": [{"action": "project.create.ui", "roles": ["ProjectManager"]}, {"action": "project.task.edit.ui", "roles": ["ProjectManager"]}], "employee": [{"action": "employee.edit.ui", "roles": ["Admin"]}]}