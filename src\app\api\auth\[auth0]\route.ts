import { handleAuth } from '@auth0/nextjs-auth0';


export const GET = handleAuth();

// import {
//   handleAuth,
//   handleCallback,
//   handleLogin,
//   handleLogout,
// } from "@auth0/nextjs-auth0";

// function getBaseUrl(webUrl: string | undefined) {
//   if (webUrl === undefined)
//     throw new Error("Error get base Url. Missing request URL.");
//   const urlObject = new URL(webUrl);
//   return `${urlObject.protocol}//${urlObject.host}`;
// }

// export const GET = handleAuth({
//   // Handle callback
//   callback: async (req, res) => {
//     const baseUrl = getBaseUrl(req.url);
//     return handleCallback(req, res, {
//       authorizationParams: {
//         audience: "https://dev-f7pr0tqd8uyni05c.us.auth0.com/api/v2/",
//         scope: "openid profile email offline_access",
//         redirect_uri: `${baseUrl}/api/auth/callback`,
//       },
//       redirectUri: `${baseUrl}/api/auth/callback`,
//     });
//   },
//   // Handle login
//   login: async (req, res) => {
//     const baseUrl = getBaseUrl(req.url);
//     return handleLogin({
//       authorizationParams: {
//         audience: "https://dev-f7pr0tqd8uyni05c.us.auth0.com/api/v2/",
//         scope: "openid profile email offline_access",
//         redirect_uri: `${baseUrl}/api/auth/callback`,
//       },
//       returnTo: baseUrl,
//     })(req, res);
//   },
//   // Handle logout
//   logout: async (req, res) => {
//     return handleLogout({
//       returnTo: getBaseUrl(req.url),
//     })(req, res);
//   },
// });
