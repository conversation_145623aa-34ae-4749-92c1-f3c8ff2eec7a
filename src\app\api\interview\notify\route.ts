import { NextRequest, NextResponse } from "next/server";
import axios from "axios";
import { ConfidentialClientApplication } from '@azure/msal-node';

const config = {
  auth: {
    clientId: process.env.CLIENT_ID!,
    authority: `https://login.microsoftonline.com/${process.env.TENANT_ID}`,
    clientSecret: process.env.CLIENT_SECRET!,
  },
};

const cca = new ConfidentialClientApplication(config);

interface InterviewNotificationData {
  candidate_id: string;
  candidate_name: string;
  candidate_email: string;
  interviewer_id: string;
  interviewer_name: string;
  interviewer_email: string;
  level: string;
  scheduled_date: string;
  scheduled_time: string;
  timezone: string;
  job_title: string;
  job_description?: string;
  resume_url?: string;
  meeting_link?: string;
  notes?: string;
}

export async function POST(req: NextRequest) {
  try {
    const body: InterviewNotificationData = await req.json();
    const {
      candidate_id,
      candidate_name,
      candidate_email,
      interviewer_id,
      interviewer_name,
      interviewer_email,
      level,
      scheduled_date,
      scheduled_time,
      timezone,
      job_title,
      job_description,
      resume_url,
      meeting_link,
      notes,
    } = body;

    // Validate required fields
    const requiredFields = [
      'candidate_id',
      'candidate_name',
      'interviewer_name',
      'interviewer_email',
      'level',
      'scheduled_date',
      'scheduled_time',
      'job_title',
    ];

    for (const field of requiredFields) {
      if (!body[field as keyof InterviewNotificationData]) {
        return NextResponse.json(
          { error: `${field} is required` },
          { status: 400 }
        );
      }
    }

    // Get access token for Microsoft Graph
    const result = await cca.acquireTokenByClientCredential({
      scopes: ['https://graph.microsoft.com/.default'],
    });

    const accessToken = result.accessToken;
    const organization = req.headers.get("X-Auth-User-Organization") || "Company";

    // Format date and time for display
    const interviewDateTime = new Date(`${scheduled_date}T${scheduled_time}:00`);
    const formattedDate = interviewDateTime.toLocaleDateString('en-US', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
    const formattedTime = interviewDateTime.toLocaleTimeString('en-US', {
      hour: '2-digit',
      minute: '2-digit',
      timeZoneName: 'short',
    });

    // Create calendar invite (.ics content)
    const startDateTime = interviewDateTime.toISOString().replace(/[-:]/g, '').split('.')[0] + 'Z';
    const endDateTime = new Date(interviewDateTime.getTime() + 60 * 60 * 1000)
      .toISOString().replace(/[-:]/g, '').split('.')[0] + 'Z';

    const icsContent = `BEGIN:VCALENDAR
VERSION:2.0
PRODID:-//AI RecruitPro//Interview Scheduler//EN
BEGIN:VEVENT
UID:interview-${candidate_id}-${level}-${Date.now()}@${organization.toLowerCase().replace(/\s+/g, '')}.com
DTSTAMP:${new Date().toISOString().replace(/[-:]/g, '').split('.')[0]}Z
DTSTART:${startDateTime}
DTEND:${endDateTime}
SUMMARY:${level.toUpperCase()} Interview - ${candidate_name}
DESCRIPTION:Interview for ${job_title} position\\nCandidate: ${candidate_name}\\nLevel: ${level}\\n${notes ? `Notes: ${notes}` : ''}
LOCATION:${meeting_link || 'Online Meeting'}
ORGANIZER:CN=${interviewer_name}:MAILTO:${interviewer_email}
ATTENDEE:CN=${candidate_name}:MAILTO:${candidate_email || '<EMAIL>'}
STATUS:CONFIRMED
SEQUENCE:0
END:VEVENT
END:VCALENDAR`;

    // Email content for interviewer
    const interviewerEmailBody = `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <div style="background-color: #f8f9fa; padding: 20px; border-radius: 8px; margin-bottom: 20px;">
          <h2 style="color: #2563eb; margin: 0;">Interview Scheduled - ${level.toUpperCase()}</h2>
          <p style="color: #6b7280; margin: 5px 0 0 0;">You have been assigned as the interviewer</p>
        </div>
        
        <div style="background-color: white; padding: 20px; border: 1px solid #e5e7eb; border-radius: 8px;">
          <h3 style="color: #374151; margin-top: 0;">Interview Details</h3>
          
          <table style="width: 100%; border-collapse: collapse;">
            <tr>
              <td style="padding: 8px 0; font-weight: bold; color: #374151;">Candidate:</td>
              <td style="padding: 8px 0; color: #6b7280;">${candidate_name}</td>
            </tr>
            <tr>
              <td style="padding: 8px 0; font-weight: bold; color: #374151;">Position:</td>
              <td style="padding: 8px 0; color: #6b7280;">${job_title}</td>
            </tr>
            <tr>
              <td style="padding: 8px 0; font-weight: bold; color: #374151;">Interview Level:</td>
              <td style="padding: 8px 0; color: #6b7280;">${level.toUpperCase()}</td>
            </tr>
            <tr>
              <td style="padding: 8px 0; font-weight: bold; color: #374151;">Date & Time:</td>
              <td style="padding: 8px 0; color: #6b7280;">${formattedDate} at ${formattedTime}</td>
            </tr>
            ${meeting_link ? `
            <tr>
              <td style="padding: 8px 0; font-weight: bold; color: #374151;">Meeting Link:</td>
              <td style="padding: 8px 0;"><a href="${meeting_link}" style="color: #2563eb;">${meeting_link}</a></td>
            </tr>
            ` : ''}
            ${notes ? `
            <tr>
              <td style="padding: 8px 0; font-weight: bold; color: #374151;">Notes:</td>
              <td style="padding: 8px 0; color: #6b7280;">${notes}</td>
            </tr>
            ` : ''}
          </table>
          
          ${job_description ? `
          <div style="margin-top: 20px;">
            <h4 style="color: #374151;">Job Description</h4>
            <div style="background-color: #f9fafb; padding: 15px; border-radius: 6px; color: #6b7280;">
              ${job_description}
            </div>
          </div>
          ` : ''}
          
          ${resume_url ? `
          <div style="margin-top: 20px;">
            <a href="${resume_url}" style="display: inline-block; background-color: #2563eb; color: white; padding: 10px 20px; text-decoration: none; border-radius: 6px;">
              📄 Download Candidate Resume
            </a>
          </div>
          ` : ''}
          
          <div style="margin-top: 20px; padding-top: 20px; border-top: 1px solid #e5e7eb;">
            <p style="color: #6b7280; font-size: 14px; margin: 0;">
              Please find the calendar invite attached. If you need to reschedule or have any questions, 
              please contact the HR team immediately.
            </p>
          </div>
        </div>
        
        <div style="text-align: center; margin-top: 20px; color: #9ca3af; font-size: 12px;">
          <p>This is an automated message from AI RecruitPro Interview Management System</p>
        </div>
      </div>
    `;

    // Send email to interviewer with calendar invite
    const emailResponse = await axios.post(
      `https://graph.microsoft.com/v1.0/users/${process.env.SENDER_EMAIL}/sendMail`,
      {
        message: {
          subject: `Interview Scheduled: ${level.toUpperCase()} - ${candidate_name} for ${job_title}`,
          body: {
            contentType: 'HTML',
            content: interviewerEmailBody,
          },
          toRecipients: [
            {
              emailAddress: {
                address: interviewer_email,
                name: interviewer_name,
              },
            },
          ],
          // CC HR team for record keeping
          ccRecipients: process.env.HR_EMAIL ? [
            {
              emailAddress: {
                address: process.env.HR_EMAIL,
              },
            }
          ] : [],
          attachments: [
            {
              '@odata.type': '#microsoft.graph.fileAttachment',
              name: `Interview_${candidate_name}_${level}.ics`,
              contentType: 'text/calendar',
              contentBytes: Buffer.from(icsContent).toString('base64'),
            },
          ],
        },
        saveToSentItems: 'true',
      },
      {
        headers: {
          Authorization: `Bearer ${accessToken}`,
          'Content-Type': 'application/json',
        },
      }
    );

    // Update schedule record to mark email as sent
    try {
      await axios.put(
        `${process.env.NODE_RED_URL}/updateInterviewSchedule`,
        {
          candidate_id,
          level,
          email_sent: true,
          email_sent_at: new Date().toISOString(),
        }
      );
    } catch (updateError) {
      console.warn("Failed to update email sent status:", updateError.message);
    }

    return NextResponse.json({
      success: true,
      message: "Interview notification sent successfully",
      data: {
        interviewer_email,
        candidate_id,
        level,
        scheduled_date,
        scheduled_time,
        email_sent_at: new Date().toISOString(),
      },
    });
  } catch (error) {
    console.error("Interview notification error:", error);
    
    return NextResponse.json(
      {
        success: false,
        error: "Failed to send interview notification",
        details: error.response?.data || error.message,
      },
      { status: 500 }
    );
  }
}
