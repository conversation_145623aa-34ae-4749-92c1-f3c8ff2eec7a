'use client';

import React from 'react';
import { 
  Card, 
  MetricCard,
  Button,
  Badge,
  DataTable
} from '@/components';
import { DashboardMetric } from '@/types';

export default function AnalyticsPage() {
  // Sample analytics data
  const metrics: DashboardMetric[] = [
    {
      id: '1',
      title: 'Total Applications',
      value: '1,247',
      change: 15,
      changeType: 'increase',
      icon: 'candidates',
      color: 'blue'
    },
    {
      id: '2',
      title: 'Hire Rate',
      value: '12.5%',
      change: 3,
      changeType: 'increase',
      icon: 'analytics',
      color: 'green'
    },
    {
      id: '3',
      title: 'Time to Hire',
      value: '18 days',
      change: -5,
      changeType: 'decrease',
      icon: 'calendar',
      color: 'orange'
    },
    {
      id: '4',
      title: 'Cost per Hire',
      value: '$3,200',
      change: -8,
      changeType: 'decrease',
      icon: 'analytics',
      color: 'purple'
    }
  ];

  const departmentData = [
    { department: 'Engineering', applications: 456, hires: 23, rate: '5.0%' },
    { department: 'Design', applications: 234, hires: 12, rate: '5.1%' },
    { department: 'Marketing', applications: 189, hires: 8, rate: '4.2%' },
    { department: 'Sales', applications: 368, hires: 19, rate: '5.2%' }
  ];

  const sourceData = [
    { source: 'LinkedIn', applications: 487, percentage: '39.1%' },
    { source: 'Indeed', applications: 312, percentage: '25.0%' },
    { source: 'Company Website', applications: 198, percentage: '15.9%' },
    { source: 'Referrals', applications: 156, percentage: '12.5%' },
    { source: 'Other', applications: 94, percentage: '7.5%' }
  ];

  const departmentColumns = [
    { key: 'department', title: 'Department', sortable: true },
    { key: 'applications', title: 'Applications', sortable: true },
    { key: 'hires', title: 'Hires', sortable: true },
    { 
      key: 'rate', 
      title: 'Hire Rate',
      render: (value: string) => (
        <Badge variant="success">{value}</Badge>
      )
    }
  ];

  const sourceColumns = [
    { key: 'source', title: 'Source', sortable: true },
    { key: 'applications', title: 'Applications', sortable: true },
    { key: 'percentage', title: 'Percentage', sortable: true }
  ];

  const handleExportReport = () => {
    console.log('Export analytics report');
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Analytics</h1>
          <p className="text-gray-600">Recruitment metrics and insights</p>
        </div>
        <Button variant="outline" onClick={handleExportReport}>
          Export Report
        </Button>
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {metrics.map((metric) => (
          <MetricCard key={metric.id} metric={metric} />
        ))}
      </div>

      {/* Charts Section */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Applications Over Time */}
        <Card title="Applications Over Time" padding="lg">
          <div className="h-64 flex items-center justify-center bg-gray-50 rounded-lg">
            <div className="text-center">
              <div className="text-gray-400 mb-2">📊</div>
              <p className="text-gray-600">Chart visualization would go here</p>
              <p className="text-sm text-gray-500">Integration with charting library needed</p>
            </div>
          </div>
        </Card>

        {/* Hiring Funnel */}
        <Card title="Hiring Funnel" padding="lg">
          <div className="space-y-4">
            <div className="flex justify-between items-center p-3 bg-blue-50 rounded-lg">
              <span className="font-medium">Applications</span>
              <span className="font-bold text-blue-600">1,247</span>
            </div>
            <div className="flex justify-between items-center p-3 bg-yellow-50 rounded-lg">
              <span className="font-medium">Screening</span>
              <span className="font-bold text-yellow-600">423</span>
            </div>
            <div className="flex justify-between items-center p-3 bg-orange-50 rounded-lg">
              <span className="font-medium">Interviews</span>
              <span className="font-bold text-orange-600">156</span>
            </div>
            <div className="flex justify-between items-center p-3 bg-green-50 rounded-lg">
              <span className="font-medium">Offers</span>
              <span className="font-bold text-green-600">78</span>
            </div>
            <div className="flex justify-between items-center p-3 bg-purple-50 rounded-lg">
              <span className="font-medium">Hires</span>
              <span className="font-bold text-purple-600">62</span>
            </div>
          </div>
        </Card>
      </div>

      {/* Department Performance */}
      <div>
        <h2 className="text-lg font-semibold text-gray-900 mb-4">Department Performance</h2>
        <DataTable
          columns={departmentColumns}
          data={departmentData}
          onSort={(key, direction) => console.log('Sort:', key, direction)}
        />
      </div>

      {/* Application Sources */}
      <div>
        <h2 className="text-lg font-semibold text-gray-900 mb-4">Application Sources</h2>
        <DataTable
          columns={sourceColumns}
          data={sourceData}
          onSort={(key, direction) => console.log('Sort:', key, direction)}
        />
      </div>

      {/* Insights */}
      <Card title="Key Insights" padding="lg">
        <div className="space-y-4">
          <div className="flex items-start space-x-3">
            <div className="w-2 h-2 bg-green-500 rounded-full mt-2"></div>
            <div>
              <p className="font-medium text-gray-900">Improved Hire Rate</p>
              <p className="text-gray-600">Your hire rate has increased by 3% this month, indicating better candidate quality.</p>
            </div>
          </div>
          <div className="flex items-start space-x-3">
            <div className="w-2 h-2 bg-blue-500 rounded-full mt-2"></div>
            <div>
              <p className="font-medium text-gray-900">LinkedIn Leading Source</p>
              <p className="text-gray-600">LinkedIn continues to be your top source of quality candidates at 39.1% of applications.</p>
            </div>
          </div>
          <div className="flex items-start space-x-3">
            <div className="w-2 h-2 bg-orange-500 rounded-full mt-2"></div>
            <div>
              <p className="font-medium text-gray-900">Faster Time to Hire</p>
              <p className="text-gray-600">Average time to hire has decreased by 5%, showing improved process efficiency.</p>
            </div>
          </div>
        </div>
      </Card>
    </div>
  );
}
