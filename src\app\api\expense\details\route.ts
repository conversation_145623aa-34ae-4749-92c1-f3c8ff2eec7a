import { ADMIN_MODE, EXPENSE_STATUS, JOBS_STATUS } from "@/Enums";
import { DynamoDBtable } from "@/lib/db_actions";
import axios from "axios";
import { NextRequest, NextResponse } from "next/server";

/**
 * Retrieves all receipts from the expense system.
 *
 * @param {NextRequest} request - The incoming request
 * @returns {Promise<Response>} - The response to the request
 * @throws {Error} - An error if the request fails
 */
export async function GET(request: NextRequest): Promise<Response> {
  try {
    const { searchParams } = new URL(request.url);
    const pageNumber = parseInt(searchParams.get("pageNumber") || "1");
    const limit = parseInt(searchParams.get("limit") || "10");
    const status = searchParams.get("status");
    let user_email = searchParams.get("user_email");
    let adminMode = searchParams.get("admin_mode");

    if (!status) {
      return new NextResponse(JSON.stringify({ error: "status is required" }), {
        headers: { "Content-Type": "application/json" },
        status: 200,
      });
    }

    const requestHeaders = new Headers(request.headers);
    if (
      requestHeaders.get("X-Auth-User-Role")?.includes("Admin") === false ||
      status === EXPENSE_STATUS.NEW ||
      adminMode === ADMIN_MODE.OFF
    ) {
      user_email = requestHeaders.get("X-Auth-User-Email")!;
    }

    console.log(user_email, "<<<user_email>>");
    console.log(requestHeaders.get("X-Auth-User-Role"), "<<<user_role>>");
    const nodeRedApiUrl = `${process.env.NODE_RED_URL}/get_all_receipt_by_filter`; // Node-RED URL
    let nodeRedResponse;

    nodeRedResponse = await axios.get(nodeRedApiUrl, {
      params: {
        status,
        ...(user_email ? { user_email } : {}),
      },
    });

    // Return the Node-RED response
    return new NextResponse(JSON.stringify(nodeRedResponse.data), {
      headers: { "Content-Type": "application/json" },
      status: 200,
    });
  } catch (error) {
    // Handle errors
    return new NextResponse(JSON.stringify({ error }), {
      headers: { "Content-Type": "application/json" },
      status: 500,
    });
  }
}

/**
 * Updates a receipt in the expense system.
 *
 * @param {NextRequest} request - The incoming request
 * @returns {Promise<Response>} - The response to the request
 * @throws {Error} - An error if the request fails
 */
export async function PUT(request: NextRequest) {
  try {
    const requestHeaders = new Headers(request.headers);
    const user_id = requestHeaders.get("X-Auth-User-ID")!;
    const email = requestHeaders.get("X-Auth-User-Email")!;

    let body = await request.json();

    let config = {
      method: "put",
      maxBodyLength: Infinity,
      url: `${process.env.NODE_RED_URL}/update_receipt`,
      headers: {
        context_user: email,
      },
      data: { ...body, name: requestHeaders.get("X-Auth-User-Name") },
    };
    console.log(requestHeaders.get("X-Auth-User-Name"));
    const response = await axios.request(config);

    return new NextResponse(JSON.stringify(response?.data), {
      headers: { "Content-Type": "application/json" },
      status: 200,
    });

    // axios
    //   .request(config)
    //   .then((response) => {
    //     console.log(JSON.stringify(response.data));
    //     return new NextResponse(JSON.stringify(response.data), {
    //       headers: { "Content-Type": "application/json" },
    //       status: 200,
    //     });
    //   })
    //   .catch((error) => {
    //     console.log(error);
    //     return new NextResponse(JSON.stringify(error), {
    //       headers: { "Content-Type": "application/json" },
    //       status: 500,
    //     });
    //   });

    // Return the Node-RED response
  } catch (error) {
    // Handle errors
    return new NextResponse(JSON.stringify({ error: error }), {
      headers: { "Content-Type": "application/json" },
      status: 500,
    });
  }
}
// export async function PUT(request: NextRequest) {
//   try {
//     // Parse the request body as JSON
//     const body = await request.json();
//     console.log("Parsed request body:", body);

//     // Validate the body with your schema
//     const validatedBody: ExpDesc = ExpDescSchema.parse(body);
//     console.log("Validated request body:", validatedBody);

//     // Extract the user ID from headers
//     const user_email = request.headers.get("X-Auth-User-Email");
//     // const user_id = "89781b7e-9511-45bf-8f04-e62e5c2510d5";
//     const jobid = body.jobdescid;

//     console.log("Extracted user_email:", user_email);

//     // Handle missing user ID
//     if (!user_email) {
//       console.error("User Email is missing from headers");
//       return NextResponse.json(
//         { message: "User Email missing in the headers." },
//         { status: 400 },
//       );
//     }

//     // Log the payload being sent
//     console.log("Payload to be sent:", { ...validatedBody, jobdescid: jobid });

//     // Make the PUT request to the external API
//     const { data } = await axios.put(`${process.env.NODE_RED_URL}/jobdes`, {
//       ...validatedBody,
//       Expdescid: id,
//     });

//     console.log("API Response Data:", data);

//     // Return the successful response
//     return NextResponse.json({ data }, { status: 200 });
//   } catch (error) {
//     // Handle schema validation errors
//     if (error instanceof z.ZodError) {
//       console.error("Validation Error:", error.errors);
//       return NextResponse.json(
//         { message: "Validation failed", errors: error.errors },
//         { status: 400 },
//       );
//     }

//     // Handle other errors (e.g., network issues, external API errors)
//     if (axios.isAxiosError(error)) {
//       console.error("Axios error:", error.response?.data || error.message);
//       return NextResponse.json(
//         {
//           message: "External API error",
//           error: error.response?.data || error.message,
//         },
//         { status: error.response?.status || 500 },
//       );
//     }

//     console.error("Unexpected error:", error);
//     return NextResponse.json(
//       { message: "An unexpected error occurred", error: error },
//       { status: 500 },
//     );
//   }
// }
