import { DynamoDBtable } from "@/lib/db_actions";
import { searchParams } from "@/utils";
import { NextRequest } from "next/server";
import { v4 as uuidv4 } from "uuid";

//* Create a new DynamoDB table instance for the "expense" table
const ExpenseTable = new DynamoDBtable("expense");

/**
 * GET request handler for fetching all expense records from the "expense" table.
 * @returns A JSON response containing the expense records or an error object.
 */
export async function GET(request: NextRequest) {
  try {
    // * <<< Querys the "expense" table for all records >>>

    // Parse the request URL query string
    const expenseType = searchParams(request, "expenseType");
    // Extract the "user_id" from the request URL query string
    let user_id = searchParams(request, "userId");
    // Extract the "travel_id" from the request URL query string
    const travel_id = searchParams(request, "travelId");
    // Extract the "user_email" from the request URL query string
    const user_email = searchParams(request, "userEmail");
    const user_name = searchParams(request, "userEmail");
    // Extract the limit from the request URL query string, or set it to 10 as default
    const limit = parseInt(searchParams(request, "limit")!) || 10;
    // Extract the pageNumber from the request URL query string, or set it to 1 as default
    const pageNumber = parseInt(searchParams(request, "pageNumber")!) || 1;

    const requestHeaders = new Headers(request.headers);
    console.log(requestHeaders.get("X-Auth-User-Role"), "<<31>>");
    if (requestHeaders.get("X-Auth-User-Role")?.includes("Employee")) {
      user_id = requestHeaders.get("X-Auth-User-ID")!;
    }

    console.log("<<<<< GET expenseType >>>>>", expenseType);
    console.log("<<<<< GET user_id >>>>>", user_id);
    console.log("<<<<< GET travel_id >>>>>", travel_id);
    console.log("<<<<< GET user_email >>>>>", user_email);
    console.log("<<<<< GET user_name >>>>>", user_name);
    console.log("<<<<< GET limit >>>>>", limit);
    console.log("<<<<< GET pageNumber >>>>>", pageNumber);

    let filterParams: any = {
      ExpressionAttributeNames: {
        // "#responseMsg": "responseMsg",
      },
      ExpressionAttributeValues: {},
    };

    if (expenseType) {
      if (filterParams.FilterExpression) {
        filterParams.FilterExpression +=
          " AND #responseMsg.#expenseType = :expenseType";
      } else {
        filterParams.FilterExpression =
          "#responseMsg.#expenseType = :expenseType";
      }
      filterParams.ExpressionAttributeNames["#expenseType"] = "expenseType";
      filterParams.ExpressionAttributeNames["#responseMsg"] = "responseMsg";
      filterParams.ExpressionAttributeValues[":expenseType"] = expenseType;
    }

    if (user_id) {
      if (filterParams.FilterExpression) {
        filterParams.FilterExpression += " AND #user_id = :user_id";
      } else {
        filterParams.FilterExpression = "#user_id = :user_id";
      }
      filterParams.ExpressionAttributeNames["#user_id"] = "user_id";
      filterParams.ExpressionAttributeValues[":user_id"] = user_id;
    }

    if (user_email) {
      if (filterParams.FilterExpression) {
        filterParams.FilterExpression += " AND #user_email = :user_email";
      } else {
        filterParams.FilterExpression = "#user_email = :user_email";
      }
      filterParams.ExpressionAttributeNames["#user_email"] = "user_email";
      filterParams.ExpressionAttributeValues[":user_email"] = user_email;
    }
    if (user_name) {
      if (filterParams.FilterExpression) {
        filterParams.FilterExpression += " AND #user_name = :user_name";
      } else {
        filterParams.FilterExpression = "#user_name = :user_name";
      }
      filterParams.ExpressionAttributeNames["#user_name"] = "user_name";
      filterParams.ExpressionAttributeValues[":user_name"] = user_name;
    }
    if (travel_id) {
      if (filterParams.FilterExpression) {
        filterParams.FilterExpression += " AND #travel_id = :travel_id";
      } else {
        filterParams.FilterExpression = "#travel_id = :travel_id";
      }
      filterParams.ExpressionAttributeNames["#travel_id"] = "travel_id";
      filterParams.ExpressionAttributeValues[":travel_id"] = travel_id;
    }
    console.log("<<<<< GET filterParams >>>>>", filterParams);

    if (!filterParams.FilterExpression) {
      // Fetch all expense records from the "expense" table
      const data = await ExpenseTable.getAll(pageNumber, limit);
      console.log("<<<<< GET >>>>>", data);
      // Return the expense records in a JSON response
      return Response.json(data);
    }
    // Fetch all expense records from the "expense" table that match the type filter
    const data = await ExpenseTable.scan(filterParams, pageNumber, limit);
    console.log("<<<<< GET Type Based Record >>>>>", data);
    // Return the expense records in a JSON response
    return Response.json(data);
  } catch (error) {
    // Return an error response if an error occurs
    return Response.json({ error });
  }
}

/**
 * POST request handler for creating a new expense record in the "expense" table.
 * @param request - The NextRequest object containing the request data.
 * @returns A JSON response containing the created expense record or an error object.
 */
export async function POST(request: NextRequest) {
  try {
    // Parse the request body as JSON
    let body = await request.json();
    // Generate a unique ID for the expense record
    const uniqueId = uuidv4();
    // Create the expense record parameters
    const requestHeaders = new Headers(request.headers);
    console.log(requestHeaders.get("X-Auth-User-ID"), "<<116>>");

    const current_user_id = requestHeaders.get("X-Auth-User-ID");
    const current_user_email = requestHeaders.get("X-Auth-User-Email");
    const current_user_name = requestHeaders.get("X-Auth-User-Name");

    let travel_id = body?.responseMsg?.travel_id;
    // delete body.responseMsg.travel_id;

    const params = {
      id: uniqueId,
      user_id: current_user_id,
      user_email: current_user_email,
      travel_id: travel_id,
      responseMsg: body?.responseMsg,
      apiResponse: body?.apiResponse,
    };
    // Create the expense record in the "expense" table
    await ExpenseTable.create(params);
    // Fetch the created expense record from the "expense" table
    const record = await ExpenseTable.getOne({ id: uniqueId });
    console.log("<<<<< POST >>>>>", record);
    // Return a JSON response containing the created expense record
    return Response.json({ ...record });
  } catch (error) {
    // Return an error response if an error occurs
    return Response.json({ error });
  }
}

/**
 * PUT request handler for updating an expense record in the "expense" table.
 * The current implementation does not perform any update logic.
 * @returns A JSON response containing an empty data object.
 */
export async function PUT(request: NextRequest) {
  try {
    // Parse the request body as JSON
    const BODY = await request.json();
    const responseMsg = BODY?.data;
    const id = BODY?.id;
    console.log(id, "<<<id>>>");
    if (!responseMsg || !id) {
      return Response.json({ error: "Missing data or id" });
    }
    if (typeof responseMsg !== "object") {
      return Response.json({ error: "Data must be an object" });
    }
    //* Update the expense record in the "expense" table
    await ExpenseTable.update(
      {
        id,
      },
      "set responseMsg = :responseMsg",
      {
        ":responseMsg": responseMsg,
      },
    );
    //* Fetch the updated expense record from the "expense" table
    const record = await ExpenseTable.getOne({ id });
    console.log("<<<<< PUT >>>>>", record);
    return Response.json(record);
  } catch (error) {
    return Response.json({ error });
  }
}

// TODO: below point
// ! Implement: User deletes only their own personal data, not that of other users.

/**
 * DELETE request handler for deleting an expense record from the "expense" table.
 * The current implementation does not perform any delete logic.
 * @returns A JSON response containing an empty data object.
 */
export async function DELETE(request: NextRequest) {
  try {
    // Parse the request body as JSON
    const BODY = await request.json();
    console.log("<<<<< DELETE BODY >>>>>", BODY);
    const id = BODY?.id;

    //* Check if the request body contains an id
    if (!id) {
      return Response.json({ error: "Missing id" });
    }

    const record = await ExpenseTable.getOne({ id });
    console.log("<<<<< DELETE Record >>>>>", record);

    if (!record) {
      return Response.json({ error: "Record not found" });
    }

    // Delete the expense record from the "expense" table
    const data = await ExpenseTable.delete({ id });
    console.log("<<<<< DELETE >>>>>", data);

    // Return a JSON response containing the deleted expense record
    return Response.json({ messsage: "Deleted" });
  } catch (error) {
    // Return an error response if an error occurs
    return Response.json({ error });
  }
}
